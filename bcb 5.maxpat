{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [35.0, 78.0, 1980.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-8", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [521.3675266504288, 295.7264987230301, 38.13247334957123, 20.0], "text": "on/off"}}, {"box": {"id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [554.0, 530.0, 33.0, 22.0], "text": "* 0.8"}}, {"box": {"id": "obj-1", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [678.6324855089188, 923.0769324302673, 45.0, 45.0]}}, {"box": {"id": "obj-137", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [530.0, 844.0, 29.5, 22.0], "text": "48"}}, {"box": {"id": "obj-135", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 0, "patching_rect": [531.0, 925.0, 49.0, 22.0], "text": "noteout"}}, {"box": {"id": "obj-134", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [531.0, 878.0, 108.0, 22.0], "text": "makenote 127 100"}}, {"box": {"id": "obj-133", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [528.0, 759.0, 24.0, 24.0]}}, {"box": {"id": "obj-131", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [528.0, 711.0, 39.0, 22.0], "text": "metro"}}, {"box": {"id": "obj-36", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [521.3675266504288, 318.8034220337868, 24.0, 24.0], "svg": ""}}, {"box": {"format": 6, "id": "obj-34", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [757.2649649381638, 519.658124923706, 50.0, 22.0]}}, {"box": {"id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [467.5213722586632, 530.0, 29.5, 22.0], "text": "- 50"}}, {"box": {"id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [543.0, 472.0, 31.0, 22.0], "text": "float"}}, {"box": {"id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [562.0, 582.0, 164.0, 22.0], "text": "if $f1 < 50 then 1000 else $f1"}}, {"box": {"id": "obj-12", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [283.76068663597107, 790.5982986092567, 50.0, 22.0]}}, {"box": {"id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [283.76068663597107, 726.4957338571548, 40.0, 22.0], "text": "line 1"}}, {"box": {"id": "obj-9", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [283.76068663597107, 675.2136820554733, 92.0, 22.0], "text": "100, 1000 1000"}}, {"box": {"id": "obj-6", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [286.26068663597107, 606.8376129865646, 35.0, 22.0], "text": "1000"}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [536.0, 404.0, 69.0, 22.0], "text": "metro 1000"}}], "lines": [{"patchline": {"destination": ["obj-12", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-131", 1], "order": 1, "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-34", 0], "midpoints": [571.5, 614.0, 834.7094037532806, 614.0, 834.7094037532806, 496.06837582588196, 766.7649649381638, 496.06837582588196], "order": 0, "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-133", 0], "source": ["obj-131", 0]}}, {"patchline": {"destination": ["obj-137", 0], "source": ["obj-133", 0]}}, {"patchline": {"destination": ["obj-135", 1], "source": ["obj-134", 1]}}, {"patchline": {"destination": ["obj-135", 0], "source": ["obj-134", 0]}}, {"patchline": {"destination": ["obj-134", 0], "source": ["obj-137", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-17", 1], "order": 1, "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-2", 1], "order": 0, "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-131", 0], "order": 1, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-2", 0], "order": 0, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-9", 0]}}], "originid": "pat-6", "dependency_cache": [], "autosave": 0}}