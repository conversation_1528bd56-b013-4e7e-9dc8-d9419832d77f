{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 77.0, 1980.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [397.0, 991.0, 31.0, 19.0], "text": "dac~"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-2", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [131.0, 340.0, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "Trebuchet MS", "fontsize": 24.0, "id": "obj-3", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [73.0, 258.0, 518.0, 34.0], "text": "Wind<PERSON><PERSON>e", "textcolor": [1.0, 1.0, 1.0, 1.0]}}, {"box": {"id": "obj-4", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [440.5, 336.434113740921, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-5", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [440.0, 397.6744247674942, 59.0, 19.0], "text": "metro 50"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-6", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [113.9534901380539, 303.87597370147705, 100.0, 17.0], "text": "Turn on windchimes"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-7", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [75.0, 340.0, 100.0, 17.0], "text": "Windspeed"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1179.0, 460.0, 47.0, 19.0], "text": "random 6"}}, {"box": {"id": "obj-9", "maxclass": "slider", "numinlets": 1, "numoutlets": 1, "orientation": 2, "outlettype": [""], "parameter_enable": 0, "patching_rect": [668.0, 460.0, 15.0, 57.0], "relative": 1, "size": 50.0}}, {"box": {"id": "obj-10", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [1303.0, 968.0, 22.0, 108.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-11", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1078.0, 977.0, 25.0, 19.0], "text": "128"}}, {"box": {"id": "obj-12", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1011.0, 979.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [875.0, 942.0, 47.0, 19.0], "text": "loadbang"}}, {"box": {"id": "obj-14", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [1240.0, 976.0, 22.0, 108.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [4.0, 44.0, 1436.0, 856.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1078.0, 680.0, 64.0, 0.0], "text": "random 157"}}, {"box": {"id": "obj-2", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [503.0, 731.0, 22.0, 108.0]}}, {"box": {"id": "obj-3", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [741.0, 723.0, 22.0, 108.0]}}, {"box": {"id": "obj-4", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [116.0, 519.0, 15.0, 15.0]}}, {"box": {"id": "obj-5", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [240.0, 500.0, 15.0, 15.0]}}, {"box": {"id": "obj-6", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [349.0, 501.0, 15.0, 15.0]}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [457.0, 497.0, 15.0, 15.0]}}, {"box": {"id": "obj-8", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [457.0, 497.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-9", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [949.0, 144.0, 70.0, 0.0], "text": "read 6.4.aif"}}, {"box": {"id": "obj-10", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [959.0, 97.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-11", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [942.0, 44.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [936.0, 198.0, 69.0, 0.0], "text": "buffer~ 6.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-13", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [889.0, 147.0, 70.0, 0.0], "text": "read 6.3.aif"}}, {"box": {"id": "obj-14", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [899.0, 100.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-15", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [882.0, 47.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [875.0, 193.0, 69.0, 0.0], "text": "buffer~ 6.c"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-17", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [822.0, 145.0, 70.0, 0.0], "text": "read 6.2.aif"}}, {"box": {"id": "obj-18", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [832.0, 98.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-19", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [815.0, 45.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [816.0, 193.0, 69.0, 0.0], "text": "buffer~ 6.b"}}, {"box": {"id": "obj-21", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [91.0, 251.0, 15.0, 15.0]}}, {"box": {"id": "obj-22", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [157.0, 250.0, 15.0, 15.0]}}, {"box": {"id": "obj-23", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [216.0, 258.0, 15.0, 15.0]}}, {"box": {"id": "obj-24", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [44.0, 257.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-25", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [451.0, 461.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [436.0, 562.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-27", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [446.0, 527.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-28", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [347.0, 463.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [332.0, 564.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-30", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [342.0, 529.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-31", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [246.0, 464.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [231.0, 565.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-33", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [241.0, 530.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [417.0, 638.0, 72.0, 0.0], "text": "groove~ 6.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-35", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [320.0, 628.0, 72.0, 0.0], "text": "groove~ 6.c"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-36", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [206.0, 287.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-37", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [153.0, 282.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-38", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [102.0, 301.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [225.0, 624.0, 72.0, 0.0], "text": "groove~ 6.b"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [213.0, 215.0, 44.0, 0.0], "text": "select 3"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [161.0, 215.0, 41.0, 0.0], "text": "select 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [113.0, 214.0, 41.0, 0.0], "text": "select 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [64.0, 213.0, 41.0, 0.0], "text": "select 0"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-44", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [167.0, 125.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [177.0, 88.0, 47.0, 0.0], "text": "random 4"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-46", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [119.0, 479.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-47", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [142.0, 587.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-48", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [114.0, 545.0, 15.0, 15.0], "svg": ""}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-49", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [54.0, 320.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"id": "obj-50", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [124.0, 42.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-51", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [762.0, 148.0, 56.0, 0.0], "text": "read 6.1.aif"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-52", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [49.0, 656.0, 58.0, 0.0], "text": "groove~ 6.a"}}, {"box": {"id": "obj-53", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [772.0, 101.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-54", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [755.0, 48.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-55", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [755.0, 192.0, 55.0, 0.0], "text": "buffer~ 6.a"}}, {"box": {"id": "obj-56", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [14.0, -3.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-57", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [11.0, -44.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [387.0, 721.0, 57.0, 0.0], "text": "random 127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-59", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [143.0, 788.0, 55.0, 0.0], "text": "pan2 100"}}, {"box": {"comment": "", "id": "obj-60", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [114.0, 11.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-61", "index": 2, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [212.0, 896.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-62", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [152.0, 883.0, 15.0, 15.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "midpoints": [1087.5, 713.0, 512.5, 713.0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-3", 0], "midpoints": [1087.5, 709.0, 750.5, 709.0], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-62", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-61", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 0, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-41", 0], "order": 1, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-42", 0], "order": 2, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-43", 0], "order": 3, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-1", 0], "order": 0, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-45", 0], "order": 2, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-58", 0], "order": 1, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-56", 0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-59", 2], "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-59", 1]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-27", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-9", 0]}}], "originid": "pat-34"}, "patching_rect": [1375.0, 903.0, 60.0, 19.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [4.0, 44.0, 1436.0, 856.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1197.0, 630.0, 64.0, 0.0], "text": "random 128"}}, {"box": {"id": "obj-2", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [625.0, 682.0, 22.0, 108.0]}}, {"box": {"id": "obj-3", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [863.0, 674.0, 22.0, 108.0]}}, {"box": {"id": "obj-4", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [110.0, 491.0, 15.0, 15.0]}}, {"box": {"id": "obj-5", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [244.0, 483.0, 15.0, 15.0]}}, {"box": {"id": "obj-6", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [345.0, 477.0, 15.0, 15.0]}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [447.0, 482.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-8", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [949.0, 124.0, 70.0, 0.0], "text": "read 5.4.aif"}}, {"box": {"id": "obj-9", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [959.0, 77.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-10", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [942.0, 24.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [936.0, 178.0, 69.0, 0.0], "text": "buffer~ 5.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-12", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [889.0, 127.0, 70.0, 0.0], "text": "read 5.3.aif"}}, {"box": {"id": "obj-13", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [899.0, 80.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-14", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [882.0, 27.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [875.0, 173.0, 69.0, 0.0], "text": "buffer~ 5.c"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-16", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [822.0, 125.0, 70.0, 0.0], "text": "read 5.2.aif"}}, {"box": {"id": "obj-17", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [832.0, 78.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-18", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [815.0, 25.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [816.0, 173.0, 69.0, 0.0], "text": "buffer~ 5.b"}}, {"box": {"id": "obj-20", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [91.0, 231.0, 15.0, 15.0]}}, {"box": {"id": "obj-21", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [157.0, 230.0, 15.0, 15.0]}}, {"box": {"id": "obj-22", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [216.0, 238.0, 15.0, 15.0]}}, {"box": {"id": "obj-23", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [44.0, 237.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-24", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [451.0, 441.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [436.0, 542.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-26", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [446.0, 507.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-27", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [347.0, 443.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [332.0, 544.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-29", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [342.0, 509.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-30", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [246.0, 444.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [231.0, 545.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-32", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [241.0, 510.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-33", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [417.0, 618.0, 72.0, 0.0], "text": "groove~ 5.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [320.0, 608.0, 72.0, 0.0], "text": "groove~ 5.c"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-35", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [206.0, 267.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-36", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [153.0, 262.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-37", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [102.0, 281.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-38", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [223.0, 604.0, 72.0, 0.0], "text": "groove~ 5.b"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [213.0, 195.0, 44.0, 0.0], "text": "select 3"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [161.0, 195.0, 41.0, 0.0], "text": "select 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [113.0, 194.0, 41.0, 0.0], "text": "select 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [64.0, 193.0, 41.0, 0.0], "text": "select 0"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-43", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [167.0, 105.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [177.0, 68.0, 47.0, 0.0], "text": "random 4"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-45", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [119.0, 459.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-46", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [142.0, 567.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-47", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [114.0, 525.0, 15.0, 15.0], "svg": ""}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-48", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [54.0, 300.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"id": "obj-49", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [124.0, 22.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-50", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [762.0, 128.0, 56.0, 0.0], "text": "read 5.1.aif"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-51", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [49.0, 636.0, 58.0, 0.0], "text": "groove~ 5.a"}}, {"box": {"id": "obj-52", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [772.0, 81.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-53", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [755.0, 28.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-54", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [755.0, 172.0, 55.0, 0.0], "text": "buffer~ 5.a"}}, {"box": {"id": "obj-55", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [14.0, -23.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-56", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [11.0, -64.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-57", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [540.0, 596.0, 57.0, 0.0], "text": "random 127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [143.0, 768.0, 55.0, 0.0], "text": "pan2 100"}}, {"box": {"comment": "", "id": "obj-59", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [114.0, -9.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-60", "index": 2, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [212.0, 876.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-61", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [152.0, 863.0, 15.0, 15.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "midpoints": [1206.5, 664.0, 634.5, 664.0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-3", 0], "midpoints": [1206.5, 660.0, 872.5, 660.0], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-61", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-60", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-39", 0], "order": 0, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 1, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-41", 0], "order": 2, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-42", 0], "order": 3, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-43", 0], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-46", 0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-1", 0], "order": 0, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-44", 0], "order": 2, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-57", 0], "order": 1, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-54", 0], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-58", 2], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-58", 1]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-9", 0]}}], "originid": "pat-41"}, "patching_rect": [1303.0, 903.0, 60.0, 19.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [6.0, 44.0, 1436.0, 856.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1103.0, 574.0, 64.0, 0.0], "text": "random 157"}}, {"box": {"id": "obj-2", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [531.0, 626.0, 22.0, 108.0]}}, {"box": {"id": "obj-3", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [769.0, 618.0, 22.0, 108.0]}}, {"box": {"id": "obj-4", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [116.0, 441.0, 15.0, 15.0]}}, {"box": {"id": "obj-5", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [235.0, 430.0, 15.0, 15.0]}}, {"box": {"id": "obj-6", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [354.0, 428.0, 15.0, 15.0]}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [469.0, 429.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-8", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [949.0, 74.0, 70.0, 0.0], "text": "read 4.4.aif"}}, {"box": {"id": "obj-9", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [959.0, 27.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-10", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [942.0, -26.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [936.0, 128.0, 69.0, 0.0], "text": "buffer~ 4.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-12", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [889.0, 77.0, 70.0, 0.0], "text": "read 4.3.aif"}}, {"box": {"id": "obj-13", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [899.0, 30.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-14", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [882.0, -23.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [875.0, 123.0, 69.0, 0.0], "text": "buffer~ 4.c"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-16", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [822.0, 75.0, 70.0, 0.0], "text": "read 4.2.aif"}}, {"box": {"id": "obj-17", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [832.0, 28.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-18", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [815.0, -25.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [816.0, 123.0, 69.0, 0.0], "text": "buffer~ 4.b"}}, {"box": {"id": "obj-20", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [91.0, 181.0, 15.0, 15.0]}}, {"box": {"id": "obj-21", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [157.0, 180.0, 15.0, 15.0]}}, {"box": {"id": "obj-22", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [216.0, 188.0, 15.0, 15.0]}}, {"box": {"id": "obj-23", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [44.0, 187.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-24", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [451.0, 391.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [436.0, 492.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-26", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [446.0, 457.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-27", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [347.0, 393.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [332.0, 494.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-29", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [342.0, 459.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-30", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [246.0, 394.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [231.0, 495.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-32", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [241.0, 460.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-33", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [417.0, 568.0, 72.0, 0.0], "text": "groove~ 4.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [320.0, 558.0, 72.0, 0.0], "text": "groove~ 4.c"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-35", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [206.0, 217.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-36", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [153.0, 212.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-37", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [102.0, 231.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-38", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [223.0, 554.0, 72.0, 0.0], "text": "groove~ 4.b"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [213.0, 145.0, 44.0, 0.0], "text": "select 3"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [161.0, 145.0, 41.0, 0.0], "text": "select 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [113.0, 144.0, 41.0, 0.0], "text": "select 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [64.0, 143.0, 41.0, 0.0], "text": "select 0"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-43", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [167.0, 55.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [177.0, 18.0, 47.0, 0.0], "text": "random 4"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-45", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [117.0, 410.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-46", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [142.0, 517.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-47", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [114.0, 475.0, 15.0, 15.0], "svg": ""}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-48", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [54.0, 250.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"id": "obj-49", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [124.0, -28.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-50", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [762.0, 78.0, 56.0, 0.0], "text": "read 4.1.aif"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-51", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [47.0, 586.0, 58.0, 0.0], "text": "groove~ 4.a"}}, {"box": {"id": "obj-52", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [772.0, 31.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-53", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [755.0, -22.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-54", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [755.0, 122.0, 55.0, 0.0], "text": "buffer~ 4.a"}}, {"box": {"id": "obj-55", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [14.0, -73.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-56", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [11.0, -114.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-57", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [318.0, 733.0, 57.0, 0.0], "text": "random 127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [143.0, 718.0, 55.0, 0.0], "text": "pan2 100"}}, {"box": {"comment": "", "id": "obj-59", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [114.0, -59.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-60", "index": 2, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [212.0, 826.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-61", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [152.0, 813.0, 15.0, 15.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "midpoints": [1112.5, 608.0, 540.5, 608.0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-3", 0], "midpoints": [1112.5, 604.0, 778.5, 604.0], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-61", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-60", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-39", 0], "order": 0, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 1, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-41", 0], "order": 2, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-42", 0], "order": 3, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-43", 0], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-46", 0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-1", 0], "order": 0, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-44", 0], "order": 2, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-57", 0], "order": 1, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-54", 0], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-58", 2], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-58", 1]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-9", 0]}}], "originid": "pat-48"}, "patching_rect": [1236.0, 900.0, 60.0, 19.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [193.0, 45.0, 1436.0, 856.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1032.0, 576.0, 64.0, 0.0], "text": "random 157"}}, {"box": {"id": "obj-2", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [460.0, 628.0, 22.0, 108.0]}}, {"box": {"id": "obj-3", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [698.0, 620.0, 22.0, 108.0]}}, {"box": {"id": "obj-4", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [114.0, 451.0, 15.0, 15.0]}}, {"box": {"id": "obj-5", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [239.0, 436.0, 15.0, 15.0]}}, {"box": {"id": "obj-6", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [352.0, 417.0, 15.0, 15.0]}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [446.0, 422.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-8", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [949.0, 74.0, 70.0, 0.0], "text": "read 3.4.aif"}}, {"box": {"id": "obj-9", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [959.0, 27.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-10", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [942.0, -26.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [938.0, 121.0, 69.0, 0.0], "text": "buffer~ 3.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-12", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [889.0, 77.0, 70.0, 0.0], "text": "read 3.3.aif"}}, {"box": {"id": "obj-13", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [899.0, 30.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-14", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [882.0, -23.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [875.0, 123.0, 69.0, 0.0], "text": "buffer~ 3.c"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-16", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [822.0, 75.0, 70.0, 0.0], "text": "read 3.2.aif"}}, {"box": {"id": "obj-17", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [832.0, 28.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-18", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [815.0, -25.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [816.0, 123.0, 69.0, 0.0], "text": "buffer~ 3.b"}}, {"box": {"id": "obj-20", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [91.0, 181.0, 15.0, 15.0]}}, {"box": {"id": "obj-21", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [157.0, 180.0, 15.0, 15.0]}}, {"box": {"id": "obj-22", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [216.0, 188.0, 15.0, 15.0]}}, {"box": {"id": "obj-23", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [44.0, 187.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-24", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [451.0, 391.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [436.0, 492.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-26", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [446.0, 457.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-27", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [347.0, 361.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [332.0, 494.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-29", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [342.0, 459.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-30", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [246.0, 394.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [231.0, 495.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-32", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [241.0, 460.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-33", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [417.0, 568.0, 72.0, 0.0], "text": "groove~ 3.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [320.0, 558.0, 72.0, 0.0], "text": "groove~ 3.c"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-35", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [206.0, 217.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-36", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [153.0, 212.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-37", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [102.0, 231.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-38", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [223.0, 554.0, 72.0, 0.0], "text": "groove~ 3.b"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [213.0, 145.0, 44.0, 0.0], "text": "select 3"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [161.0, 145.0, 41.0, 0.0], "text": "select 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [113.0, 144.0, 41.0, 0.0], "text": "select 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [64.0, 143.0, 41.0, 0.0], "text": "select 0"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-43", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [167.0, 55.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [177.0, 18.0, 47.0, 0.0], "text": "random 4"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-45", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [119.0, 409.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-46", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [142.0, 517.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-47", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [114.0, 475.0, 15.0, 15.0], "svg": ""}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-48", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [54.0, 250.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"id": "obj-49", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [124.0, -28.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-50", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [760.0, 83.0, 56.0, 0.0], "text": "read 3.1.aif"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-51", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [47.0, 586.0, 58.0, 0.0], "text": "groove~ 3.a"}}, {"box": {"id": "obj-52", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [772.0, 31.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-53", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [755.0, -22.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-54", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [755.0, 122.0, 55.0, 0.0], "text": "buffer~ 3.a"}}, {"box": {"id": "obj-55", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [14.0, -73.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-56", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [11.0, -114.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-57", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [329.0, 736.0, 57.0, 0.0], "text": "random 127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [143.0, 718.0, 55.0, 0.0], "text": "pan2 100"}}, {"box": {"comment": "", "id": "obj-59", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [114.0, -59.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-60", "index": 2, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [212.0, 826.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-61", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [152.0, 813.0, 15.0, 15.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "midpoints": [1041.5, 610.0, 469.5, 610.0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-3", 0], "midpoints": [1041.5, 606.0, 707.5, 606.0], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-61", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-60", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-39", 0], "order": 0, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 1, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-41", 0], "order": 2, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-42", 0], "order": 3, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-43", 0], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-46", 0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-1", 0], "order": 0, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-44", 0], "order": 2, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-57", 0], "order": 1, "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-54", 0], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-58", 2], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-58", 1]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-9", 0]}}], "originid": "pat-55"}, "patching_rect": [1165.0, 900.0, 60.0, 19.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "signal"], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [4.0, 44.0, 1436.0, 856.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1132.0, 548.0, 64.0, 0.0], "text": "random 157"}}, {"box": {"id": "obj-2", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [749.0, 698.0, 22.0, 108.0]}}, {"box": {"id": "obj-3", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [797.0, 695.0, 22.0, 108.0]}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "id": "obj-4", "maxclass": "dial", "needlecolor": [0.0, 0.0, 0.0, 1.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "outlinecolor": [0.882352941176471, 0.882352941176471, 0.882352941176471, 1.0], "parameter_enable": 0, "patching_rect": [455.0, 800.0, 40.0, 40.0], "vtracking": 0}}, {"box": {"id": "obj-5", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [436.0, 434.0, 15.0, 15.0]}}, {"box": {"id": "obj-6", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [346.0, 444.0, 15.0, 15.0]}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [219.0, 446.0, 15.0, 15.0]}}, {"box": {"id": "obj-8", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [93.0, 451.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-9", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [939.0, 84.0, 70.0, 0.0], "text": "read 2.4.aif"}}, {"box": {"id": "obj-10", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [949.0, 37.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-11", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [932.0, -16.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [928.0, 133.0, 69.0, 0.0], "text": "buffer~ 2.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-13", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [879.0, 79.0, 70.0, 0.0], "text": "read 2.3.aif"}}, {"box": {"id": "obj-14", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [889.0, 40.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-15", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [872.0, -13.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [865.0, 133.0, 69.0, 0.0], "text": "buffer~ 2.c"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-17", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [812.0, 85.0, 70.0, 0.0], "text": "read 2.2.aif"}}, {"box": {"id": "obj-18", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [822.0, 38.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-19", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [805.0, -15.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [806.0, 133.0, 69.0, 0.0], "text": "buffer~ 2.b"}}, {"box": {"id": "obj-21", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [81.0, 191.0, 15.0, 15.0]}}, {"box": {"id": "obj-22", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [147.0, 190.0, 15.0, 15.0]}}, {"box": {"id": "obj-23", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [206.0, 198.0, 15.0, 15.0]}}, {"box": {"id": "obj-24", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [34.0, 197.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-25", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [441.0, 401.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [426.0, 502.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-27", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [436.0, 467.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-28", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [337.0, 403.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [322.0, 504.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-30", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [332.0, 469.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-31", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [236.0, 404.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [221.0, 505.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-33", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [231.0, 470.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [402.0, 573.0, 72.0, 0.0], "text": "groove~ 2.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-35", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [310.0, 568.0, 72.0, 0.0], "text": "groove~ 2.c"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-36", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [196.0, 227.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-37", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [143.0, 222.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-38", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [92.0, 241.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [209.0, 564.0, 72.0, 0.0], "text": "groove~ 2.b"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [203.0, 155.0, 44.0, 0.0], "text": "select 3"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [151.0, 155.0, 41.0, 0.0], "text": "select 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [103.0, 154.0, 41.0, 0.0], "text": "select 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [54.0, 153.0, 41.0, 0.0], "text": "select 0"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-44", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [157.0, 65.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [167.0, 28.0, 47.0, 0.0], "text": "random 4"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-46", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [109.0, 419.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-47", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [132.0, 527.0, 25.0, 0.0], "text": "sig~"}}, {"box": {"id": "obj-48", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [104.0, 485.0, 15.0, 15.0], "svg": ""}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-49", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [44.0, 260.0, 35.0, 0.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"id": "obj-50", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [114.0, -18.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-51", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [751.0, 92.0, 56.0, 0.0], "text": "read 2.1.aif"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-52", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [37.0, 596.0, 58.0, 0.0], "text": "groove~ 2.a"}}, {"box": {"id": "obj-53", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [762.0, 41.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-54", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [745.0, -12.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-55", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [745.0, 132.0, 55.0, 0.0], "text": "buffer~ 2.a"}}, {"box": {"id": "obj-56", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [4.0, -63.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-57", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1.0, -104.0, 45.0, 0.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [329.0, 707.0, 57.0, 0.0], "text": "random 127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-59", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [133.0, 728.0, 55.0, 0.0], "text": "pan2 100"}}, {"box": {"comment": "", "id": "obj-60", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [104.0, -49.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-61", "index": 2, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [441.0, 715.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-62", "index": 2, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [202.0, 836.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-63", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [142.0, 823.0, 15.0, 15.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "midpoints": [1141.5, 582.0, 758.5, 582.0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-3", 0], "midpoints": [1141.5, 578.0, 806.5, 578.0], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-63", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-62", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 0, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-41", 0], "order": 1, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-42", 0], "order": 2, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-43", 0], "order": 3, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-27", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-1", 0], "order": 0, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-45", 0], "order": 2, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-58", 0], "order": 1, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-56", 0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-4", 0], "order": 0, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-59", 2], "order": 1, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-59", 1]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-61", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-9", 0]}}], "originid": "pat-62"}, "patching_rect": [1073.0, 898.0, 60.0, 19.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "signal"], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [464.0, 152.0, 1436.0, 856.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [751.0, 850.0, 64.0, 19.0], "text": "random 157"}}, {"box": {"id": "obj-2", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [179.0, 902.0, 22.0, 108.0]}}, {"box": {"id": "obj-3", "interpinlet": 1, "maxclass": "gain~", "multichannelvariant": 0, "numinlets": 2, "numoutlets": 2, "orientation": 2, "outlettype": ["signal", ""], "parameter_enable": 0, "patching_rect": [417.0, 894.0, 22.0, 108.0]}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "id": "obj-4", "maxclass": "dial", "needlecolor": [0.0, 0.0, 0.0, 1.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "outlinecolor": [0.882352941176471, 0.882352941176471, 0.882352941176471, 1.0], "parameter_enable": 0, "patching_rect": [476.0, 863.0, 40.0, 40.0], "vtracking": 0}}, {"box": {"id": "obj-5", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [255.0, 542.0, 15.0, 15.0]}}, {"box": {"id": "obj-6", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [356.0, 542.0, 15.0, 15.0]}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [458.0, 546.0, 15.0, 15.0]}}, {"box": {"id": "obj-8", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [146.0, 554.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-9", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [938.0, 189.0, 70.0, 19.0], "text": "read 1.4.aif"}}, {"box": {"id": "obj-10", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [948.0, 142.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-11", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [931.0, 89.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [927.0, 238.0, 69.0, 19.0], "text": "buffer~ 1.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-13", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [878.0, 192.0, 70.0, 19.0], "text": "read 1.3.aif"}}, {"box": {"id": "obj-14", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [888.0, 145.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-15", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [871.0, 92.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [864.0, 238.0, 69.0, 19.0], "text": "buffer~ 1.c"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-17", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [811.0, 192.0, 70.0, 19.0], "text": "read 1.2.aif"}}, {"box": {"id": "obj-18", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [821.0, 143.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-19", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [804.0, 90.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [805.0, 238.0, 69.0, 19.0], "text": "buffer~ 1.b"}}, {"box": {"id": "obj-21", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [80.0, 296.0, 15.0, 15.0]}}, {"box": {"id": "obj-22", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [146.0, 295.0, 15.0, 15.0]}}, {"box": {"id": "obj-23", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [205.0, 303.0, 15.0, 15.0]}}, {"box": {"id": "obj-24", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [33.0, 302.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-25", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [440.0, 506.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [425.0, 607.0, 25.0, 19.0], "text": "sig~"}}, {"box": {"id": "obj-27", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [435.0, 572.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-28", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [336.0, 508.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [321.0, 609.0, 25.0, 19.0], "text": "sig~"}}, {"box": {"id": "obj-30", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [331.0, 574.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-31", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [235.0, 509.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [220.0, 610.0, 25.0, 19.0], "text": "sig~"}}, {"box": {"id": "obj-33", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [230.0, 575.0, 15.0, 15.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [401.0, 678.0, 72.0, 19.0], "text": "groove~ 1.d"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-35", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [309.0, 673.0, 72.0, 19.0], "text": "groove~ 1.c"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-36", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [195.0, 332.0, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-37", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [142.0, 327.0, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-38", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [91.0, 346.0, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [205.0, 669.0, 72.0, 19.0], "text": "groove~ 1.b"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [202.0, 260.0, 44.0, 19.0], "text": "select 3"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [150.0, 260.0, 41.0, 19.0], "text": "select 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [102.0, 259.0, 41.0, 19.0], "text": "select 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [53.0, 258.0, 41.0, 19.0], "text": "select 0"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-44", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [156.0, 170.0, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-45", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [166.0, 133.0, 47.0, 19.0], "text": "random 4"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-46", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [108.0, 524.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-47", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [131.0, 632.0, 25.0, 19.0], "text": "sig~"}}, {"box": {"id": "obj-48", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [103.0, 590.0, 15.0, 15.0], "svg": ""}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-49", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [43.0, 365.0, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"id": "obj-50", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [113.0, 87.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-51", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [751.0, 193.0, 56.0, 19.0], "text": "read 1.1.aif"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-52", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [36.0, 701.0, 58.0, 19.0], "text": "groove~ 1.a"}}, {"box": {"id": "obj-53", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [761.0, 146.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-54", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [744.0, 93.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-55", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [744.0, 237.0, 55.0, 19.0], "text": "buffer~ 1.a"}}, {"box": {"id": "obj-56", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [3.0, 42.0, 15.0, 15.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-57", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [0.0, 1.0, 45.0, 29.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [507.0, 805.0, 57.0, 19.0], "text": "random 127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-59", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [333.0, 855.0, 55.0, 19.0], "text": "pan2 100"}}, {"box": {"comment": "", "id": "obj-60", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [103.0, 56.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-61", "index": 2, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [678.0, 670.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-62", "index": 2, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [374.0, 1066.0, 15.0, 15.0]}}, {"box": {"comment": "", "id": "obj-63", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [146.0, 1077.0, 15.0, 15.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "midpoints": [760.5, 884.0, 188.5, 884.0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-3", 0], "midpoints": [760.5, 880.0, 426.5, 880.0], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-63", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-62", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-59", 0], "midpoints": [410.5, 765.0, 342.5, 765.0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-59", 0], "midpoints": [318.5, 762.0, 342.5, 762.0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-34", 0], "midpoints": [204.5, 429.0, 410.5, 429.0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-35", 0], "midpoints": [151.5, 507.0, 318.5, 507.0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-39", 0], "midpoints": [100.5, 515.0, 214.5, 515.0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-59", 0], "midpoints": [214.5, 760.0, 342.5, 760.0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-40", 0], "midpoints": [165.5, 222.0, 211.5, 222.0], "order": 0, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-41", 0], "midpoints": [165.5, 222.0, 159.5, 222.0], "order": 1, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-42", 0], "midpoints": [165.5, 222.0, 111.5, 222.0], "order": 2, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-43", 0], "midpoints": [165.5, 221.0, 62.5, 221.0], "order": 3, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-52", 0], "midpoints": [52.5, 540.0, 45.5, 540.0], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [122.0, 457.0, 760.5, 457.0], "order": 0, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-45", 0], "order": 2, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-58", 0], "midpoints": [122.0, 443.0, 516.5, 443.0], "order": 1, "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-59", 0], "midpoints": [45.5, 776.0, 342.5, 776.0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-56", 0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-4", 0], "order": 0, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-59", 2], "order": 1, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-59", 1]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-58", 0], "midpoints": [687.0, 745.0, 516.5, 745.0], "source": ["obj-61", 0]}}, {"patchline": {"destination": ["obj-27", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-9", 0]}}], "originid": "pat-106"}, "patching_rect": [986.0, 897.0, 60.0, 19.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-21", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 7, "numoutlets": 7, "outlettype": ["bang", "bang", "bang", "bang", "bang", "bang", ""], "patching_rect": [1343.0, 649.0, 92.0, 19.0], "text": "select 0 1 2 3 4 5"}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-22", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1221.0, 516.0, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-23", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [380.62016093730927, 352.713183760643, 35.0, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [186.0465145111084, 664.3410955667496, 66.0, 19.0], "text": "random 35"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [658.0, 404.0, 82.0, 19.0], "text": "counter 0 0 50"}}, {"box": {"id": "obj-26", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [836.0, 797.0, 15.0, 15.0]}}, {"box": {"bgcolor": [0.866666666666667, 0.866666666666667, 0.866666666666667, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-27", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [667.5, 544.961248755455, 22.8970703125, 19.0], "textcolor": [0.0, 0.0, 0.0, 1.0], "triscale": 0.9}}, {"box": {"color": [1.0, 0.890196078431372, 0.090196078431373, 1.0], "fontname": "<PERSON><PERSON>", "fontsize": 9.0, "id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [667.5, 702.3255922794342, 41.0, 19.0], "text": "select 0"}}], "lines": [{"patchline": {"destination": ["obj-1", 1], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-10", 0], "order": 0, "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-14", 0], "order": 1, "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-12", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-10", 0], "midpoints": [1384.5, 953.0, 1312.5, 953.0], "order": 0, "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-14", 0], "midpoints": [1384.5, 951.0, 1249.5, 951.0], "order": 1, "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-10", 0], "midpoints": [1312.5, 953.0, 1312.5, 953.0], "order": 0, "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-14", 0], "midpoints": [1312.5, 951.0, 1249.5, 951.0], "order": 1, "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-10", 0], "midpoints": [1245.5, 951.0, 1312.5, 951.0], "order": 0, "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-14", 0], "midpoints": [1245.5, 950.0, 1249.5, 950.0], "order": 1, "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-10", 0], "midpoints": [1174.5, 951.0, 1312.5, 951.0], "order": 0, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-14", 0], "midpoints": [1174.5, 950.0, 1249.5, 950.0], "order": 1, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-10", 0], "midpoints": [1082.5, 950.0, 1312.5, 950.0], "order": 0, "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-14", 0], "midpoints": [1082.5, 949.0, 1249.5, 949.0], "order": 1, "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-24", 1], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-10", 0], "midpoints": [1036.5, 950.0, 1312.5, 950.0], "source": ["obj-20", 1]}}, {"patchline": {"destination": ["obj-14", 0], "midpoints": [995.5, 948.0, 1249.5, 948.0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-15", 0], "midpoints": [1413.3333333333333, 785.0, 1384.5, 785.0], "source": ["obj-21", 5]}}, {"patchline": {"destination": ["obj-16", 0], "midpoints": [1401.1666666666667, 785.0, 1312.5, 785.0], "source": ["obj-21", 4]}}, {"patchline": {"destination": ["obj-17", 0], "midpoints": [1389.0, 784.0, 1245.5, 784.0], "source": ["obj-21", 3]}}, {"patchline": {"destination": ["obj-18", 0], "midpoints": [1376.8333333333333, 784.0, 1174.5, 784.0], "source": ["obj-21", 2]}}, {"patchline": {"destination": ["obj-19", 0], "midpoints": [1364.6666666666667, 783.0, 1082.5, 783.0], "source": ["obj-21", 1]}}, {"patchline": {"destination": ["obj-20", 0], "midpoints": [1352.5, 782.0, 995.5, 782.0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-21", 0], "midpoints": [1230.5, 590.0, 1352.5, 590.0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-5", 1], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-24", 0], "midpoints": [845.0, 618.3643373250961, 195.5465145111084, 618.3643373250961], "order": 1, "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-8", 0], "order": 0, "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-28", 0], "midpoints": [677.0, 663.0, 677.0, 663.0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-26", 0], "midpoints": [677.0, 750.0, 845.0, 750.0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-5", 0], "hidden": 1, "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-27", 0], "source": ["obj-9", 0]}}], "originid": "pat-33", "dependency_cache": [{"name": "pan2.maxpat", "bootpath": "~/AppData/Roaming/Cycling '74/Max 9/examples/spatialization/panning/lib", "patcherrelativepath": "../../AppData/Roaming/Cycling '74/Max 9/examples/spatialization/panning/lib", "type": "JSON", "implicit": 1}], "autosave": 0}}