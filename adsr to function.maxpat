{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [321.0, 238.0, 1000.0, 780.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [70.0, 169.0, 69.0, 22.0], "save": ["#N", "thispatcher", ";", "#Q", "end", ";"], "text": "thispatcher"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-29", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [70.0, 149.0, 87.0, 22.0], "text": "presentation 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-20", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [95.0, 125.0, 65.0, 20.0], "text": "Press Me!"}}, {"box": {"id": "obj-16", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [70.0, 125.0, 20.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 5, "outlettype": ["float", "float", "float", "float", "float"], "patching_rect": [416.0, 107.0, 116.0, 22.0], "text": "unpack 0. 0. 0. 0. 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [416.0, 70.0, 155.0, 22.0], "text": "loadmess 25 1 60 0.75 200"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-32", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [436.0, 309.0, 67.0, 22.0], "text": "domain $1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-30", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [131.0, 299.0, 76.0, 22.0], "text": "fix 0 1, 0 0 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [422.0, 166.0, 32.5, 22.0], "text": "+ 5."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-27", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [422.0, 201.0, 76.0, 22.0], "text": "minimum $1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [300.0, 116.0, 32.5, 22.0], "text": "+ 5."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [315.0, 166.0, 32.5, 22.0], "text": "- 5."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-24", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [315.0, 188.0, 79.0, 22.0], "text": "maximum $1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-22", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [208.0, 149.0, 32.5, 22.0], "text": "- 5."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-21", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [208.0, 188.0, 79.0, 22.0], "text": "maximum $1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-19", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 138.0, 76.0, 22.0], "text": "minimum $1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-17", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [315.0, 338.0, 69.0, 22.0], "text": "sustain 3 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [315.0, 299.0, 32.5, 22.0], "text": "t b l"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-14", "maxclass": "flonum", "minimum": 65.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [422.0, 232.0, 50.0, 22.0], "presentation": 1, "presentation_rect": [300.0, 115.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [422.0, 268.0, 81.0, 22.0], "text": "sprintf 3 %f 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-8", "maxclass": "flonum", "maximum": 1.0, "minimum": 0.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [367.0, 208.0, 50.0, 22.0], "presentation": 1, "presentation_rect": [245.0, 115.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-9", "maxclass": "flonum", "maximum": 195.0, "minimum": 30.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [315.0, 232.0, 50.0, 22.0], "presentation": 1, "presentation_rect": [193.0, 115.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [315.0, 261.0, 71.0, 22.0], "text": "pak 0. 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [315.0, 275.0, 65.0, 22.0], "text": "prepend 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-7", "maxclass": "flonum", "maximum": 1.0, "minimum": 0.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [260.0, 223.0, 50.0, 22.0], "presentation": 1, "presentation_rect": [138.0, 115.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-5", "maxclass": "flonum", "maximum": 55.0, "minimum": 5.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [208.0, 223.0, 50.0, 22.0], "presentation": 1, "presentation_rect": [86.0, 115.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [208.0, 261.0, 71.0, 22.0], "text": "pak 0. 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [209.0, 293.0, 65.0, 22.0], "text": "prepend 1"}}, {"box": {"addpoints": [0.0, 0.0, 0, 25.0, 1.0, 0, 60.0, 0.75, 2, 200.0, 0.0, 0], "autosustain": 1, "classic_curve": 1, "clickadd": 0, "clickmove": 0, "clicksustain": 0, "domain": 200.0, "id": "obj-1", "legend": 0, "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [208.0, 466.0, 198.0, 109.0], "presentation": 1, "presentation_rect": [86.0, 137.0, 264.0, 109.0]}}], "lines": [{"patchline": {"destination": ["obj-11", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [431.5, 432.0, 217.5, 432.0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-12", 0], "order": 1, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-23", 0], "order": 2, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-32", 0], "order": 0, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [338.0, 392.0, 217.5, 392.0], "source": ["obj-15", 1]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-29", 0], "order": 2, "source": ["obj-16", 0]}}, {"patchline": {"color": [0.0, 0.0, 0.0, 0.145098], "destination": ["obj-30", 0], "order": 1, "source": ["obj-16", 0]}}, {"patchline": {"color": [0.0, 0.0, 0.0, 0.121569], "destination": ["obj-4", 0], "order": 0, "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [324.5, 378.0, 217.5, 378.0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-27", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [140.5, 321.0, 217.5, 321.0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [445.5, 360.0, 217.5, 360.0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-25", 0], "order": 0, "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-3", 0], "order": 1, "source": ["obj-5", 0]}}, {"patchline": {"color": [0.0, 0.0, 0.0, 0.121569], "destination": ["obj-14", 0], "source": ["obj-6", 4]}}, {"patchline": {"color": [0.0, 0.0, 0.0, 0.121569], "destination": ["obj-5", 0], "source": ["obj-6", 0]}}, {"patchline": {"color": [0.0, 0.0, 0.0, 0.121569], "destination": ["obj-7", 0], "source": ["obj-6", 1]}}, {"patchline": {"color": [0.0, 0.0, 0.0, 0.121569], "destination": ["obj-8", 0], "source": ["obj-6", 3]}}, {"patchline": {"color": [0.0, 0.0, 0.0, 0.121569], "destination": ["obj-9", 0], "source": ["obj-6", 2]}}, {"patchline": {"destination": ["obj-3", 1], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-10", 1], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-10", 0], "order": 1, "source": ["obj-9", 0]}}, {"patchline": {"destination": ["obj-22", 0], "order": 2, "source": ["obj-9", 0]}}, {"patchline": {"destination": ["obj-26", 0], "order": 0, "source": ["obj-9", 0]}}], "originid": "pat-277", "dependency_cache": [], "autosave": 0}}