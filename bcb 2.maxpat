{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 77.0, 1980.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-1", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [678.6324855089188, 923.0769324302673, 45.0, 45.0]}}, {"box": {"id": "obj-137", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [530.0, 844.0, 29.5, 22.0], "text": "48"}}, {"box": {"id": "obj-135", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 0, "patching_rect": [531.0, 925.0, 49.0, 22.0], "text": "noteout"}}, {"box": {"id": "obj-134", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [531.0, 878.0, 108.0, 22.0], "text": "makenote 127 100"}}, {"box": {"id": "obj-133", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [528.0, 759.0, 24.0, 24.0]}}, {"box": {"id": "obj-131", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [528.0, 711.0, 39.0, 22.0], "text": "metro"}}, {"box": {"id": "obj-36", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [527.0, 646.0, 24.0, 24.0], "svg": ""}}, {"box": {"format": 6, "id": "obj-34", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [685.0, 530.0, 50.0, 22.0]}}, {"box": {"id": "obj-24", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [586.0, 314.0, 35.0, 22.0], "text": "1000"}}, {"box": {"id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [561.0, 632.0, 31.0, 22.0], "text": "float"}}, {"box": {"id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [552.0, 525.0, 29.5, 22.0], "text": "- 50"}}, {"box": {"id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [543.0, 472.0, 31.0, 22.0], "text": "float"}}, {"box": {"id": "obj-16", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [536.0, 358.0, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [562.0, 582.0, 164.0, 22.0], "text": "if $f1 < 50 then 1000 else $f1"}}, {"box": {"id": "obj-12", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [345.0, 780.0, 50.0, 22.0]}}, {"box": {"id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [345.0, 716.0, 40.0, 22.0], "text": "line 1"}}, {"box": {"id": "obj-9", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [345.0, 665.0, 92.0, 22.0], "text": "100, 1000 1000"}}, {"box": {"id": "obj-6", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [402.0, 325.0, 35.0, 22.0], "text": "1000"}}, {"box": {"id": "obj-4", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [470.0, 325.0, 50.0, 22.0]}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [536.0, 404.0, 69.0, 22.0], "text": "metro 1000"}}], "lines": [{"patchline": {"destination": ["obj-12", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-133", 0], "source": ["obj-131", 0]}}, {"patchline": {"destination": ["obj-137", 0], "source": ["obj-133", 0]}}, {"patchline": {"destination": ["obj-135", 1], "source": ["obj-134", 1]}}, {"patchline": {"destination": ["obj-135", 0], "source": ["obj-134", 0]}}, {"patchline": {"destination": ["obj-134", 0], "source": ["obj-137", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-131", 1], "order": 1, "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-34", 0], "order": 0, "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-2", 1], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-17", 1], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-131", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-9", 0]}}], "originid": "pat-2402", "dependency_cache": [], "autosave": 0}}