{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [599.0, 213.0, 997.0, 780.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-44", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [283.0, 22.0, 111.0, 18.0], "text": "bounce away"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-43", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [321.0, 185.0, 81.0, 18.0], "text": "bounce factor"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "format": 6, "id": "obj-42", "maxclass": "flonum", "maximum": 7.0, "minimum": 1.1, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [267.0, 185.0, 50.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "format": 6, "id": "obj-40", "maxclass": "flonum", "maximum": 0.99, "minimum": 0.01, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [281.0, 313.0, 50.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 11.595187, "id": "obj-39", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [176.0, 60.0, 32.5, 22.0], "text": "5."}}, {"box": {"id": "obj-38", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [223.0, 6.0, 50.0, 50.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-37", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [223.0, 128.0, 32.5, 20.0], "text": "int"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [236.0, 101.0, 42.0, 20.0], "text": "* 1000."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "format": 6, "id": "obj-35", "maxclass": "flonum", "maximum": 30.0, "minimum": 0.1, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [176.0, 93.33333027362823, 50.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-34", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [282.0, 73.0, 111.0, 18.0], "text": "bounce duration (Sec)"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-33", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [334.0, 315.0, 66.0, 18.0], "text": "feedback"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [63.0, 36.0, 52.0, 20.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [223.0, 248.0, 51.0, 20.0], "text": "*~ 10000"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [222.0, 207.0, 38.0, 20.0], "text": "log~ 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [174.0, 284.0, 92.0, 20.0], "text": "receive~ feedback"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [173.0, 401.0, 81.0, 20.0], "text": "send~ feedback"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [66.0, 379.0, 37.0, 20.0], "text": "*~ 0.3"}}, {"box": {"id": "obj-14", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [36.0, 388.0, 20.0, 20.0], "svg": ""}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [66.0, 413.0, 49.0, 20.0], "text": "dac~ 1 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [173.0, 352.0, 128.0, 20.0], "text": "*~ 0.99"}}, {"box": {"id": "obj-11", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [170.0, 245.0, 20.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 11.595187, "id": "obj-17", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [64.0, 176.0, 150.0, 22.0], "text": "set 0.25 0.5 0.75 1 0.67 0.3"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 11.595187, "id": "obj-21", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [65.0, 296.0, 40.0, 22.0], "text": "click~"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-5", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [170.0, 150.0, 47.0, 20.0], "text": "10, 1 $1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [222.0, 177.0, 32.5, 20.0], "text": "line~"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 10.0, "id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [173.0, 319.0, 71.0, 20.0], "text": "delay~ 88200"}}], "lines": [{"patchline": {"destination": ["obj-21", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-3", 1], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-15", 0], "order": 1, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-3", 0], "order": 0, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-12", 0], "order": 0, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-15", 0], "order": 1, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-17", 0], "order": 1, "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-39", 0], "order": 0, "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-37", 1], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-37", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-12", 1], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-19", 1], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-11", 0], "order": 1, "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-4", 0], "order": 0, "source": ["obj-5", 0]}}], "originid": "pat-626", "dependency_cache": [], "autosave": 0}}