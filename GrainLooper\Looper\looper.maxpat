{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 0, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [981.0, 160.0, 680.0, 684.0], "openinpresentation": 1, "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-42", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [1103.4, 485.86, 45.0, 45.0]}}, {"box": {"id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1427.0, 246.86000000000013, 90.0, 22.0], "text": "loadmess 2000"}}, {"box": {"activefgdialcolor": [0.57636836783545, 0.576368229540612, 0.576368265679262, 1.0], "appearance": 2, "bordercolor": [1.0, 1.0, 1.0, 1.0], "fgdialcolor": [0.0, 0.0, 0.0, 1.0], "focusbordercolor": [0.109803921568627, 0.109803921568627, 0.109803921568627, 1.0], "id": "obj-2", "maxclass": "live.dial", "needlecolor": [0.0, 0.0, 0.0, 1.0], "numinlets": 1, "numoutlets": 2, "outlettype": ["", "float"], "parameter_enable": 1, "patching_rect": [1427.0, 283.8600000000001, 50.0, 80.0], "presentation": 1, "presentation_rect": [541.0714234113693, 3.820757567882538, 50.0, 80.0], "saved_attribute_attributes": {"activefgdialcolor": {"expression": ""}, "bordercolor": {"expression": ""}, "fgdialcolor": {"expression": ""}, "focusbordercolor": {"expression": ""}, "needlecolor": {"expression": ""}, "valueof": {"parameter_longname": "Speed", "parameter_mmax": 5000.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "Speed", "parameter_type": 0, "parameter_unitstyle": 0}}, "varname": "live.dial"}}, {"box": {"id": "obj-33", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1141.8604828119278, 1066.6666831970215, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-5", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1290.5949454307556, 806.9828863143921, 121.0, 22.0], "text": "set Synthgrain-<PERSON><PERSON><PERSON>"}}, {"box": {"format": 6, "id": "obj-17", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2462.1358885765076, 156.3106774687767, 50.0, 22.0]}}, {"box": {"id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [2453.4, 207.77, 66.0, 22.0], "text": "cycle~ 100"}}, {"box": {"automatic": 1, "bgcolor": [0.0, 0.0, 0.0, 1.0], "bufsize": 8, "calccount": 722, "fgcolor": [1.0, 1.0, 1.0, 1.0], "gridcolor": [0.0, 0.0, 0.0, 1.0], "id": "obj-7", "maxclass": "scope~", "numinlets": 2, "numoutlets": 0, "patching_rect": [2437.4998557567596, 266.28569197654724, 130.0, 130.0], "presentation": 1, "presentation_rect": [379.61164528131485, 6.320757567882538, 147.57281351089478, 75.0]}}, {"box": {"id": "obj-52", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2281.874782383442, 141.24998652935028, 35.0, 22.0], "text": "1000"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-48", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2209.3747892975807, 106.24998986721039, 99.0, 23.0], "text": "0.1, 127. 10100"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-49", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [2209.3747892975807, 185.6249822974205, 95.0, 23.0], "text": "line 0."}}, {"box": {"floatoutput": 1, "id": "obj-45", "knobcolor": [1.0, 0.0, 0.0, 1.0], "knobshape": 5, "maxclass": "slider", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [2205.37644135952, 279.56990480422974, 168.8172117471695, 26.88172161579132], "presentation": 1, "presentation_rect": [184.06873607635498, 5.660377621650696, 168.8172117471695, 75.66037994623184]}}, {"box": {"id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [3022.8569626808167, 1091.4285063743591, 58.0, 22.0], "text": "loadbang"}}, {"box": {"floatoutput": 1, "id": "obj-40", "maxclass": "rslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 0, "patching_rect": [1080.0316836833954, 996.9071606397629, 150.51545548439026, 24.74226665496826], "presentation": 1, "presentation_rect": [5.454545259475708, 256.7010165452957, 195.8762776851654, 24.74226665496826], "size": 10000.0}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", "bang"], "patching_rect": [1840.697608590126, 171.92982292175293, 32.0, 22.0], "text": "t b b"}}, {"box": {"id": "obj-24", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [6.49350643157959, 62.33766174316406, 150.0, 20.0], "presentation": 1, "presentation_rect": [5.454545259475708, 62.33766174316406, 92.37288355827332, 20.0], "text": "1. ADDA"}}, {"box": {"id": "obj-22", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [116.94915533065796, 39.830509424209595, 150.0, 20.0], "presentation": 1, "presentation_rect": [123.58491140604019, 61.32075756788254, 43.79821640253067, 20.0], "text": "2.loop"}}, {"box": {"id": "obj-16", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [496.7742154598236, 117.20430624485016, 150.0, 20.0], "presentation": 1, "presentation_rect": [646.7532405853271, 114.99999475479126, 61.29032528400421, 20.0], "text": "Delay"}}, {"box": {"id": "obj-12", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [551.6129275560379, 202.15054655075073, 150.0, 20.0], "presentation": 1, "presentation_rect": [646.7532405853271, 447.9896647930145, 61.29032528400421, 20.0], "text": "<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-9", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1790.6976103782654, 36.2499965429306, 72.4999930858612, 72.4999930858612], "presentation": 1, "presentation_rect": [123.58491140604019, 5.660377621650696, 43.79821640253067, 43.79821640253067]}}, {"box": {"id": "obj-236", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2139.645025253296, 785.7988367080688, 24.0, 24.0]}}, {"box": {"id": "obj-235", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2074.5562661886215, 785.7988367080688, 24.0, 24.0]}}, {"box": {"id": "obj-234", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2013.0178030729294, 785.7988367080688, 24.0, 24.0]}}, {"box": {"id": "obj-233", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [2146.7456171512604, 747.9290132522583, 61.0, 22.0], "text": "delay 100"}}, {"box": {"id": "obj-232", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [2080.473426103592, 747.9290132522583, 61.0, 22.0], "text": "delay 100"}}, {"box": {"id": "obj-231", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [2013.0178030729294, 747.9290132522583, 61.0, 22.0], "text": "delay 100"}}, {"box": {"id": "obj-230", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1975.7396956086159, 780.4733927845955, 24.0, 24.0]}}, {"box": {"id": "obj-228", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 4, "outlettype": ["bang", "bang", "bang", ""], "patching_rect": [1975.7396956086159, 724.2603735923767, 54.0, 22.0], "text": "sel 1 2 0"}}, {"box": {"id": "obj-227", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1975.7396956086159, 689.3491300940514, 59.0, 22.0], "text": "random 3"}}, {"box": {"id": "obj-226", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1415.0652677416801, 1450.8175197839737, 70.0, 22.0], "text": "loadmess 1"}}, {"box": {"id": "obj-225", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1347.0, 508.8600000000001, 67.0, 22.0], "text": "delay 4000"}}, {"box": {"id": "obj-224", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", "bang"], "patching_rect": [1364.0, 475.8600000000001, 32.0, 22.0], "text": "t b b"}}, {"box": {"id": "obj-223", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [989.9166307449341, 928.8889331817627, 44.0, 22.0], "text": "sel 0 1"}}, {"box": {"id": "obj-222", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [989.9166307449341, 901.8130774497986, 29.5, 22.0], "text": "> 0"}}, {"box": {"id": "obj-221", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1011.3206739425659, 963.7352757453918, 50.0, 22.0], "text": "4000."}}, {"box": {"id": "obj-218", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [916.087245464325, 880.8130774497986, 29.5, 22.0], "text": "t f f"}}, {"box": {"id": "obj-216", "maxclass": "ezadc~", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [1724.9999341964722, 63.74998962879181, 45.0, 45.0], "presentation": 1, "presentation_rect": [5.454545259475708, 5.08474588394165, 45.0, 45.0]}}, {"box": {"id": "obj-215", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [2036.5385295152664, 41.34615522623062, 70.0, 22.0], "text": "loadmess 1"}}, {"box": {"id": "obj-211", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [968.837245464325, 963.7352757453918, 29.5, 22.0], "text": "0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-208", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [916.087245464325, 963.2352757453918, 35.0, 23.0], "text": "sig~"}}, {"box": {"id": "obj-198", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1252.0100367069244, 1374.8502821922302, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-193", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [916.087245464325, 747.6190404891968, 76.23791253566742, 76.23791253566742]}}, {"box": {"id": "obj-191", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [1363.0, 443.8600000000001, 34.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-190", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1462.0, 432.8600000000001, 50.0, 22.0], "text": "2"}}, {"box": {"id": "obj-183", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [1836.363570690155, 698.0, 34.0, 22.0], "text": "sel 2"}}, {"box": {"direction": 0, "id": "obj-182", "maxclass": "live.grid", "numinlets": 2, "numoutlets": 6, "outlettype": ["", "", "", "", "", ""], "parameter_enable": 1, "patching_rect": [1836.363570690155, 628.5713911056519, 484.9314715862274, 49.315064907073975], "presentation": 1, "presentation_rect": [5.454545259475708, 432.9896664619446, 632.6043027639389, 49.99999666213989], "rows": 2, "saved_attribute_attributes": {"valueof": {"parameter_invisible": 1, "parameter_longname": "live.grid[1]", "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.grid", "parameter_type": 3}}, "varname": "live.grid[1]"}}, {"box": {"id": "obj-181", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [916.087245464325, 696.428564786911, 34.0, 22.0], "text": "sel 2"}}, {"box": {"id": "obj-180", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1243.0, 472.8600000000001, 29.5, 22.0], "text": "0"}}, {"box": {"id": "obj-178", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1422.0, 511.8600000000001, 35.0, 22.0], "text": "0 0 0"}}, {"box": {"id": "obj-176", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1363.0, 417.8600000000001, 33.0, 22.0], "text": "== 2"}}, {"box": {"id": "obj-174", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [1307.0, 265.8600000000001, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-172", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1307.0, 324.8600000000001, 56.0, 22.0], "text": "metro 2n"}}, {"box": {"direction": 0, "id": "obj-171", "maxclass": "live.grid", "numinlets": 2, "numoutlets": 6, "outlettype": ["", "", "", "", "", ""], "parameter_enable": 1, "patching_rect": [1307.0, 628.5713911056519, 484.9314715862274, 49.315064907073975], "presentation": 1, "presentation_rect": [5.454545259475708, 99.99999642372131, 632.6043027639389, 49.99999666213989], "rows": 2, "saved_attribute_attributes": {"valueof": {"parameter_invisible": 1, "parameter_longname": "live.grid", "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.grid", "parameter_type": 3}}, "varname": "live.grid"}}, {"box": {"id": "obj-170", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [1307.0, 380.8600000000001, 75.0, 22.0], "text": "counter 1 16"}}, {"box": {"id": "obj-169", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [916.087245464325, 851.3698011040688, 126.0, 22.0], "text": "random @range -2. 2."}}, {"box": {"floatoutput": 1, "id": "obj-163", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1500.934567809105, 1123.364477276802, 40.0, 40.0], "presentation": 1, "presentation_rect": [514.7328672409058, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"floatoutput": 1, "id": "obj-162", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1445.0652677416801, 1123.364477276802, 40.0, 40.0], "presentation": 1, "presentation_rect": [445.6607061624527, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"floatoutput": 1, "id": "obj-161", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1392.1213429570198, 1123.364477276802, 40.0, 40.0], "presentation": 1, "presentation_rect": [373.4957617521286, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"floatoutput": 1, "id": "obj-160", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1327.3082593083382, 1123.364477276802, 40.0, 40.0], "presentation": 1, "presentation_rect": [302.36174511909485, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"floatoutput": 1, "id": "obj-159", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1271.5605962276459, 1123.364477276802, 40.0, 40.0], "presentation": 1, "presentation_rect": [228.13494515419006, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"floatoutput": 1, "id": "obj-158", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1209.878353714943, 1123.364477276802, 40.0, 40.0], "presentation": 1, "presentation_rect": [154.93907296657562, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"floatoutput": 1, "id": "obj-157", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1150.999849498272, 1123.364477276802, 40.0, 40.0], "presentation": 1, "presentation_rect": [85.86691188812256, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"floatoutput": 1, "id": "obj-156", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [1085.2521864175797, 1127.857072353363, 40.0, 40.0], "presentation": 1, "presentation_rect": [16.794750809669495, 167.01029992103577, 40.0, 40.0], "size": 15000.0}}, {"box": {"id": "obj-153", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1211.5471391677856, 952.5772662162781, 97.0, 22.0], "text": "loadmess 10000"}}, {"box": {"id": "obj-152", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1080.0316836833954, 952.5772662162781, 70.0, 22.0], "text": "loadmess 0"}}, {"box": {"format": 6, "id": "obj-151", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1063.5659079551697, 1053.2467432022095, 61.68627846240997, 22.0]}}, {"box": {"format": 6, "id": "obj-150", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [987.0129776000977, 1053.2467432022095, 50.0, 22.0]}}, {"box": {"id": "obj-139", "maxclass": "preset", "numinlets": 1, "numoutlets": 5, "outlettype": ["preset", "int", "preset", "int", ""], "patching_rect": [1415.0652677416801, 1486.666578054428, 100.0, 40.0], "preset_data": [{"number": 1, "data": [5, "obj-41", "toggle", "int", 1, 5, "obj-533", "live.gain~", "float", -0.066532336175442, 5, "obj-515", "number", "int", 1015, 5, "obj-513", "number", "int", 442, 5, "obj-507", "number", "float", 1.455686569213867, 5, "obj-491", "number", "int", 226, 5, "obj-490", "number", "int", 3472, 6, "obj-483", "rslider", "list", 1015, 226, 6, "obj-481", "rslider", "list", 442, 3472, 5, "obj-476", "number", "int", 11500, 5, "obj-73", "live.gain~", "float", 0.102631650865078, 5, "obj-119", "number", "float", 0.0, 5, "obj-120", "number", "float", 250.0, 5, "obj-121", "number", "float", 4625.0, 5, "obj-122", "number", "float", 4875.0, 5, "obj-123", "number", "float", 5500.0, 5, "obj-124", "number", "float", 4750.0, 5, "obj-125", "number", "float", 4875.0, 5, "obj-126", "number", "float", 4000.0, 5, "obj-150", "number", "float", 0.0, 5, "obj-151", "number", "float", 4000.0, 5, "obj-156", "dial", "float", 0.0, 5, "obj-157", "dial", "float", 250.0, 5, "obj-158", "dial", "float", 4625.0, 5, "obj-159", "dial", "float", 4875.0, 5, "obj-160", "dial", "float", 5500.0, 5, "obj-161", "dial", "float", 4750.0, 5, "obj-162", "dial", "float", 4875.0, 5, "obj-163", "dial", "float", 4000.0, 5, "obj-171", "live.grid", "mode", 0, 5, "obj-171", "live.grid", "matrixmode", 0, 5, "obj-171", "live.grid", "columns", 16, 5, "obj-171", "live.grid", "rows", 2, 7, "obj-171", "live.grid", "constraint", 1, 1, 1, 7, "obj-171", "live.grid", "constraint", 2, 1, 1, 7, "obj-171", "live.grid", "constraint", 3, 1, 1, 7, "obj-171", "live.grid", "constraint", 4, 1, 1, 7, "obj-171", "live.grid", "constraint", 5, 1, 1, 7, "obj-171", "live.grid", "constraint", 6, 1, 1, 7, "obj-171", "live.grid", "constraint", 7, 1, 1, 7, "obj-171", "live.grid", "constraint", 8, 1, 1, 7, "obj-171", "live.grid", "constraint", 9, 1, 1, 7, "obj-171", "live.grid", "constraint", 10, 1, 1, 7, "obj-171", "live.grid", "constraint", 11, 1, 1, 7, "obj-171", "live.grid", "constraint", 12, 1, 1, 7, "obj-171", "live.grid", "constraint", 13, 1, 1, 7, "obj-171", "live.grid", "constraint", 14, 1, 1, 7, "obj-171", "live.grid", "constraint", 15, 1, 1, 7, "obj-171", "live.grid", "constraint", 16, 1, 1, 20, "obj-171", "live.grid", "steps", 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 20, "obj-171", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, "obj-174", "toggle", "int", 0, 5, "obj-182", "live.grid", "mode", 0, 5, "obj-182", "live.grid", "matrixmode", 0, 5, "obj-182", "live.grid", "columns", 16, 5, "obj-182", "live.grid", "rows", 2, 7, "obj-182", "live.grid", "constraint", 1, 1, 1, 7, "obj-182", "live.grid", "constraint", 2, 1, 1, 7, "obj-182", "live.grid", "constraint", 3, 1, 1, 7, "obj-182", "live.grid", "constraint", 4, 1, 1, 7, "obj-182", "live.grid", "constraint", 5, 1, 1, 7, "obj-182", "live.grid", "constraint", 6, 1, 1, 7, "obj-182", "live.grid", "constraint", 7, 1, 1, 7, "obj-182", "live.grid", "constraint", 8, 1, 1, 7, "obj-182", "live.grid", "constraint", 9, 1, 1, 7, "obj-182", "live.grid", "constraint", 10, 1, 1, 7, "obj-182", "live.grid", "constraint", 11, 1, 1, 7, "obj-182", "live.grid", "constraint", 12, 1, 1, 7, "obj-182", "live.grid", "constraint", 13, 1, 1, 7, "obj-182", "live.grid", "constraint", 14, 1, 1, 7, "obj-182", "live.grid", "constraint", 15, 1, 1, 7, "obj-182", "live.grid", "constraint", 16, 1, 1, 20, "obj-182", "live.grid", "steps", 1, 2, 1, 2, 1, 1, 1, 1, 1, 2, 2, 1, 2, 2, 2, 1, 20, "obj-182", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"number": 2, "data": [5, "obj-41", "toggle", "int", 1, 5, "obj-533", "live.gain~", "float", -0.066532336175442, 5, "obj-515", "number", "int", 4826, 5, "obj-513", "number", "int", 4866, 5, "obj-507", "number", "float", 2.044173240661621, 5, "obj-491", "number", "int", 7709, 5, "obj-490", "number", "int", 4970, 6, "obj-483", "rslider", "list", 4826, 7709, 6, "obj-481", "rslider", "list", 4866, 4970, 5, "obj-476", "number", "int", 11500, 5, "obj-73", "live.gain~", "float", 0.102631650865078, 5, "obj-119", "number", "float", 0.0, 5, "obj-120", "number", "float", 250.0, 5, "obj-121", "number", "float", 4625.0, 5, "obj-122", "number", "float", 4875.0, 5, "obj-123", "number", "float", 7750.0, 5, "obj-124", "number", "float", 8500.0, 5, "obj-125", "number", "float", 12000.0, 5, "obj-126", "number", "float", 4000.0, 5, "obj-150", "number", "float", 0.0, 5, "obj-151", "number", "float", 4000.0, 5, "obj-156", "dial", "float", 0.0, 5, "obj-157", "dial", "float", 250.0, 5, "obj-158", "dial", "float", 4625.0, 5, "obj-159", "dial", "float", 4875.0, 5, "obj-160", "dial", "float", 7750.0, 5, "obj-161", "dial", "float", 8500.0, 5, "obj-162", "dial", "float", 12000.0, 5, "obj-163", "dial", "float", 4000.0, 5, "obj-171", "live.grid", "mode", 0, 5, "obj-171", "live.grid", "matrixmode", 0, 5, "obj-171", "live.grid", "columns", 16, 5, "obj-171", "live.grid", "rows", 2, 7, "obj-171", "live.grid", "constraint", 1, 1, 1, 7, "obj-171", "live.grid", "constraint", 2, 1, 1, 7, "obj-171", "live.grid", "constraint", 3, 1, 1, 7, "obj-171", "live.grid", "constraint", 4, 1, 1, 7, "obj-171", "live.grid", "constraint", 5, 1, 1, 7, "obj-171", "live.grid", "constraint", 6, 1, 1, 7, "obj-171", "live.grid", "constraint", 7, 1, 1, 7, "obj-171", "live.grid", "constraint", 8, 1, 1, 7, "obj-171", "live.grid", "constraint", 9, 1, 1, 7, "obj-171", "live.grid", "constraint", 10, 1, 1, 7, "obj-171", "live.grid", "constraint", 11, 1, 1, 7, "obj-171", "live.grid", "constraint", 12, 1, 1, 7, "obj-171", "live.grid", "constraint", 13, 1, 1, 7, "obj-171", "live.grid", "constraint", 14, 1, 1, 7, "obj-171", "live.grid", "constraint", 15, 1, 1, 7, "obj-171", "live.grid", "constraint", 16, 1, 1, 20, "obj-171", "live.grid", "steps", 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 20, "obj-171", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, "obj-174", "toggle", "int", 1, 5, "obj-182", "live.grid", "mode", 0, 5, "obj-182", "live.grid", "matrixmode", 0, 5, "obj-182", "live.grid", "columns", 16, 5, "obj-182", "live.grid", "rows", 2, 7, "obj-182", "live.grid", "constraint", 1, 1, 1, 7, "obj-182", "live.grid", "constraint", 2, 1, 1, 7, "obj-182", "live.grid", "constraint", 3, 1, 1, 7, "obj-182", "live.grid", "constraint", 4, 1, 1, 7, "obj-182", "live.grid", "constraint", 5, 1, 1, 7, "obj-182", "live.grid", "constraint", 6, 1, 1, 7, "obj-182", "live.grid", "constraint", 7, 1, 1, 7, "obj-182", "live.grid", "constraint", 8, 1, 1, 7, "obj-182", "live.grid", "constraint", 9, 1, 1, 7, "obj-182", "live.grid", "constraint", 10, 1, 1, 7, "obj-182", "live.grid", "constraint", 11, 1, 1, 7, "obj-182", "live.grid", "constraint", 12, 1, 1, 7, "obj-182", "live.grid", "constraint", 13, 1, 1, 7, "obj-182", "live.grid", "constraint", 14, 1, 1, 7, "obj-182", "live.grid", "constraint", 15, 1, 1, 7, "obj-182", "live.grid", "constraint", 16, 1, 1, 20, "obj-182", "live.grid", "steps", 1, 2, 1, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 1, 20, "obj-182", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"number": 3, "data": [5, "obj-41", "toggle", "int", 1, 5, "obj-533", "live.gain~", "float", -0.066532336175442, 5, "obj-515", "number", "int", 4826, 5, "obj-513", "number", "int", 4866, 5, "obj-507", "number", "float", 2.044173240661621, 5, "obj-491", "number", "int", 7709, 5, "obj-490", "number", "int", 4970, 6, "obj-483", "rslider", "list", 4826, 7709, 6, "obj-481", "rslider", "list", 4866, 4970, 5, "obj-476", "number", "int", 11500, 5, "obj-73", "live.gain~", "float", 0.102631650865078, 5, "obj-119", "number", "float", 0.0, 5, "obj-120", "number", "float", 250.0, 5, "obj-121", "number", "float", 4625.0, 5, "obj-122", "number", "float", 4875.0, 5, "obj-123", "number", "float", 7750.0, 5, "obj-124", "number", "float", 8500.0, 5, "obj-125", "number", "float", 12000.0, 5, "obj-126", "number", "float", 4000.0, 5, "obj-150", "number", "float", 0.0, 5, "obj-151", "number", "float", 4000.0, 5, "obj-156", "dial", "float", 0.0, 5, "obj-157", "dial", "float", 250.0, 5, "obj-158", "dial", "float", 4625.0, 5, "obj-159", "dial", "float", 4875.0, 5, "obj-160", "dial", "float", 7750.0, 5, "obj-161", "dial", "float", 8500.0, 5, "obj-162", "dial", "float", 12000.0, 5, "obj-163", "dial", "float", 4000.0, 5, "obj-171", "live.grid", "mode", 0, 5, "obj-171", "live.grid", "matrixmode", 0, 5, "obj-171", "live.grid", "columns", 16, 5, "obj-171", "live.grid", "rows", 2, 7, "obj-171", "live.grid", "constraint", 1, 1, 1, 7, "obj-171", "live.grid", "constraint", 2, 1, 1, 7, "obj-171", "live.grid", "constraint", 3, 1, 1, 7, "obj-171", "live.grid", "constraint", 4, 1, 1, 7, "obj-171", "live.grid", "constraint", 5, 1, 1, 7, "obj-171", "live.grid", "constraint", 6, 1, 1, 7, "obj-171", "live.grid", "constraint", 7, 1, 1, 7, "obj-171", "live.grid", "constraint", 8, 1, 1, 7, "obj-171", "live.grid", "constraint", 9, 1, 1, 7, "obj-171", "live.grid", "constraint", 10, 1, 1, 7, "obj-171", "live.grid", "constraint", 11, 1, 1, 7, "obj-171", "live.grid", "constraint", 12, 1, 1, 7, "obj-171", "live.grid", "constraint", 13, 1, 1, 7, "obj-171", "live.grid", "constraint", 14, 1, 1, 7, "obj-171", "live.grid", "constraint", 15, 1, 1, 7, "obj-171", "live.grid", "constraint", 16, 1, 1, 20, "obj-171", "live.grid", "steps", 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 20, "obj-171", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, "obj-174", "toggle", "int", 1, 5, "obj-182", "live.grid", "mode", 0, 5, "obj-182", "live.grid", "matrixmode", 0, 5, "obj-182", "live.grid", "columns", 16, 5, "obj-182", "live.grid", "rows", 2, 7, "obj-182", "live.grid", "constraint", 1, 1, 1, 7, "obj-182", "live.grid", "constraint", 2, 1, 1, 7, "obj-182", "live.grid", "constraint", 3, 1, 1, 7, "obj-182", "live.grid", "constraint", 4, 1, 1, 7, "obj-182", "live.grid", "constraint", 5, 1, 1, 7, "obj-182", "live.grid", "constraint", 6, 1, 1, 7, "obj-182", "live.grid", "constraint", 7, 1, 1, 7, "obj-182", "live.grid", "constraint", 8, 1, 1, 7, "obj-182", "live.grid", "constraint", 9, 1, 1, 7, "obj-182", "live.grid", "constraint", 10, 1, 1, 7, "obj-182", "live.grid", "constraint", 11, 1, 1, 7, "obj-182", "live.grid", "constraint", 12, 1, 1, 7, "obj-182", "live.grid", "constraint", 13, 1, 1, 7, "obj-182", "live.grid", "constraint", 14, 1, 1, 7, "obj-182", "live.grid", "constraint", 15, 1, 1, 7, "obj-182", "live.grid", "constraint", 16, 1, 1, 20, "obj-182", "live.grid", "steps", 1, 2, 1, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 1, 20, "obj-182", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}]}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-138", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.Gigaverb.maxpat", "numinlets": 2, "numoutlets": 2, "offset": [0.0, 0.0], "outlettype": ["signal", "signal"], "patching_rect": [1053.3333082199097, 1872.499955356121, 332.0, 116.0], "presentation": 1, "presentation_rect": [237.41329514980316, 256.7010165452957, 332.0, 116.0], "varname": "bp.<PERSON><PERSON><PERSON><PERSON>[1]", "viewvisibility": 1}}, {"box": {"id": "obj-129", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [941.6666307449341, 1088.5416251420975, 149.0, 22.0], "text": "groove~ S<PERSON><PERSON><PERSON>-<PERSON><PERSON>er"}}, {"box": {"format": 6, "id": "obj-126", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1503.9437719583511, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [507.51637279987335, 226.80411100387573, 61.86440825462341, 22.0]}}, {"box": {"format": 6, "id": "obj-125", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1445.0652677416801, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [435.35142838954926, 226.80411100387573, 61.86440825462341, 22.0]}}, {"box": {"format": 6, "id": "obj-124", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1387.1213429570198, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [362.1555562019348, 226.80411100387573, 61.86440825462341, 22.0]}}, {"box": {"format": 6, "id": "obj-123", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1327.3082593083382, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [291.02153956890106, 226.80411100387573, 61.86440825462341, 22.0]}}, {"box": {"format": 6, "id": "obj-122", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1266.5605962276459, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [216.79473960399628, 226.80411100387573, 61.86440825462341, 22.0]}}, {"box": {"format": 6, "id": "obj-121", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1204.878353714943, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [144.62979519367218, 226.80411100387573, 61.86440825462341, 22.0]}}, {"box": {"format": 6, "id": "obj-120", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1145.999849498272, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [74.52670633792877, 225.7731832265854, 61.86440825462341, 22.0]}}, {"box": {"format": 6, "id": "obj-119", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1085.2521864175797, 1183.1775609254837, 50.0, 22.0], "presentation": 1, "presentation_rect": [5.454545259475708, 225.7731832265854, 61.86440825462341, 22.0]}}, {"box": {"id": "obj-117", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 8, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [1126.3736814260483, 1238.317747414112, 233.63635528087616, 22.0], "text": "mc.pack~ 8"}}, {"box": {"id": "obj-116", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [1006.587245464325, 1627.4999611973763, 54.0, 22.0], "text": "cross~ 5"}}, {"box": {"id": "obj-115", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [942.4999775290489, 1627.4999611973763, 54.0, 22.0], "text": "cross~ 5"}}, {"box": {"id": "obj-109", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["multichannelsignal", "", ""], "patching_rect": [1126.3736814260483, 1306.593470454216, 96.0, 22.0], "text": "mc.m<PERSON><PERSON><PERSON>~"}}, {"box": {"id": "obj-74", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [977.4999766945839, 2000.8332856297493, 45.0, 45.0], "presentation": 1, "presentation_rect": [66.66666269302368, 5.08474588394165, 45.0, 45.0]}}, {"box": {"id": "obj-73", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [977.4999766945839, 1713.3332924842834, 48.0, 136.0], "presentation": 1, "presentation_rect": [587.0588480234146, 158.07214987277985, 53.0, 222.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[2]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~[2]", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[2]"}}, {"box": {"id": "obj-72", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [941.587245464325, 1555.5554628372192, 84.0, 22.0], "text": "mc.unpack~ 2"}}, {"box": {"id": "obj-71", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1252.0100367069244, 1447.169878602028, 104.0, 22.0], "text": "randomrange -1 1"}}, {"box": {"id": "obj-70", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [1252.0100367069244, 1481.132144331932, 108.0, 22.0], "text": "mc.sig~ @chans 8"}}, {"box": {"id": "obj-69", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [941.587245464325, 1519.99990940094, 126.0, 22.0], "text": "mc.stereo~ @chans 8"}}, {"box": {"id": "obj-68", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1557.5472422242165, 1312.2642119526863, 120.0, 22.0], "text": "randomrange 0.2 0.8"}}, {"box": {"id": "obj-66", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [1557.5472422242165, 1345.2830814123154, 108.0, 22.0], "text": "mc.sig~ @chans 8"}}, {"box": {"id": "obj-65", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [941.587245464325, 1459.9999129772186, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2434.285569190979, 1091.4285063743591, 79.0, 22.0], "text": "random 4999"}}, {"box": {"id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2414.8570091724396, 779.9999535083771, 79.0, 22.0], "text": "random 9999"}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [2448.5712826251984, 817.1428084373474, 46.0, 22.0], "text": "+ 3000"}}, {"box": {"id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [941.587245464325, 1364.444363117218, 164.0, 22.0], "text": "mc.delay~ 900000 @chans 8"}}, {"box": {"id": "obj-21", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2682.856982946396, 1054.2856514453888, 126.0, 22.0], "text": "random @range -3. 3."}}, {"box": {"id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2242.8570091724396, 1091.4285063743591, 79.0, 22.0], "text": "random 4999"}}, {"box": {"id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2242.8570091724396, 788.5713815689087, 79.0, 22.0], "text": "random 9999"}}, {"box": {"id": "obj-18", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1836.363570690155, 796.3781012296677, 63.5294144153595, 63.5294144153595]}}, {"box": {"id": "obj-15", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2737.1426939964294, 1162.857073545456, 87.05882716178894, 20.0], "text": "random pitch"}}, {"box": {"id": "obj-14", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2582.8569889068604, 1119.9999332427979, 150.0, 20.0], "text": "random duration"}}, {"box": {"id": "obj-13", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2485.7141375541687, 862.8570914268494, 150.0, 20.0], "text": "random start stop"}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-11", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.Gigaverb.maxpat", "numinlets": 2, "numoutlets": 2, "offset": [0.0, 0.0], "outlettype": ["signal", "signal"], "patching_rect": [1911.7117105722427, 1473.8738729953766, 332.0, 116.0], "presentation": 1, "presentation_rect": [238.4442229270935, 594.8453274965286, 330.5882490873337, 116.47059309482574], "varname": "bp.Gigaverb", "viewvisibility": 1}}, {"box": {"id": "obj-687", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [1829.238570690155, 1631.531530559063, 48.25, 22.0], "text": "dac~"}}, {"box": {"id": "obj-442", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2654.285556077957, 374.28569197654724, 121.0, 22.0], "text": "set Synthgrain-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-450", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [3022.8569626808167, 1139.999932050705, 106.0, 22.0], "text": "set SynthgrainEnv"}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-455", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2688.5712683200836, 965.7142281532288, 125.0, 20.0], "text": "Envolope Generator", "textjustification": 1}}, {"box": {"id": "obj-456", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2419.9998557567596, 628.5713911056519, 131.0, 20.0], "text": "Grain Envolope"}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-459", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2242.8570091724396, 834.2856645584106, 108.39416080713272, 20.0], "presentation": 1, "presentation_rect": [11.640111923217773, 490.72162199020386, 108.39416080713272, 20.0], "text": "Start/Stop Point", "textjustification": 1}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-460", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2242.8570091724396, 1145.7142174243927, 55.10948956012726, 20.0], "presentation": 1, "presentation_rect": [355.96998953819275, 492.78347754478455, 55.10948956012726, 20.0], "text": "Duration", "textjustification": 1}}, {"box": {"buffername": "SynthgrainEnv", "id": "obj-466", "maxclass": "waveform~", "numinlets": 5, "numoutlets": 6, "outlettype": ["float", "float", "float", "float", "list", ""], "patching_rect": [3022.8569626808167, 1191.4285004138947, 256.0, 64.0]}}, {"box": {"buffername": "Synthgrain-<PERSON><PERSON>er", "id": "obj-467", "maxclass": "waveform~", "numinlets": 5, "numoutlets": 6, "outlettype": ["float", "float", "float", "float", "list", ""], "patching_rect": [2654.285556077957, 437.1428310871124, 256.0, 64.0], "presentation": 1, "presentation_rect": [11.640111923217773, 595.876255273819, 195.28285789489746, 111.76471054553986], "waveformcolor": [1.0, 1.0, 1.0, 1.0]}}, {"box": {"id": "obj-469", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2425.7141411304474, 457.14282989501953, 35.0, 22.0], "text": "clear"}}, {"box": {"id": "obj-476", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2494.2855656147003, 662.8571033477783, 126.0, 22.0]}}, {"box": {"id": "obj-478", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 10, "outlettype": ["float", "list", "float", "float", "float", "float", "float", "", "int", ""], "patching_rect": [2419.9998557567596, 602.857106924057, 131.0, 22.0], "text": "info~ Synth<PERSON>in-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-479", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2419.9998557567596, 548.5713958740234, 24.0, 24.0]}}, {"box": {"id": "obj-481", "maxclass": "rslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 0, "patching_rect": [2242.8570091724396, 1171.4285016059875, 212.24999797344208, 25.000000000000455], "presentation": 1, "presentation_rect": [355.96998953819275, 517.5257441997528, 212.24999797344208, 25.000000000000455], "size": 5000.0}}, {"box": {"id": "obj-483", "maxclass": "rslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 0, "patching_rect": [2242.8570091724396, 862.8570914268494, 191.0, 25.0], "presentation": 1, "presentation_rect": [11.640111923217773, 517.5257441997528, 191.0, 25.0], "size": 10000.0}}, {"box": {"id": "obj-490", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2437.142711877823, 1217.1427845954895, 50.0, 22.0], "presentation": 1, "presentation_rect": [519.8875061273575, 553.6082164049149, 50.0, 22.0]}}, {"box": {"id": "obj-491", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2414.285570383072, 911.4285171031952, 50.0, 22.0], "presentation": 1, "presentation_rect": [150.81536185741425, 554.6391441822052, 50.0, 22.0]}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-492", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2682.856982946396, 1159.999930858612, 50.0, 20.0], "text": "pitch"}}, {"box": {"id": "obj-494", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2437.142711877823, 1251.428496837616, 110.0, 22.0], "text": "s Synthgrain<PERSON>ur-<PERSON>"}}, {"box": {"id": "obj-495", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2414.285570383072, 962.8570854663849, 116.0, 22.0], "text": "s SynthgrainStart-Hi"}}, {"box": {"id": "obj-496", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2682.856982946396, 1234.2856407165527, 102.0, 22.0], "text": "s <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-497", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2242.8570091724396, 1254.2856395244598, 112.0, 22.0], "text": "s SynthgrainDur-Lo"}}, {"box": {"id": "obj-504", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2242.8570091724396, 962.8570854663849, 118.0, 22.0], "text": "s SynthgrainStart-Lo"}}, {"box": {"format": 6, "id": "obj-507", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2682.856982946396, 1182.857072353363, 50.0, 22.0]}}, {"box": {"id": "obj-513", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2242.8570091724396, 1217.1427845954895, 50.0, 22.0], "presentation": 1, "presentation_rect": [360.0937006473541, 553.6082164049149, 50.0, 22.0]}}, {"box": {"id": "obj-515", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2242.8570091724396, 911.4285171031952, 50.0, 22.0], "presentation": 1, "presentation_rect": [11.640111923217773, 554.6391441822052, 50.0, 22.0]}}, {"box": {"id": "obj-516", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [2828.5712599754333, 1234.2856407165527, 151.0, 22.0], "text": "buffer~ SynthgrainEnv 512"}}, {"box": {"id": "obj-517", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2828.5712599754333, 1191.4285004138947, 135.0, 22.0], "text": "fill 1 512, apply hanning"}}, {"box": {"id": "obj-518", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2334.2855751514435, 457.14282989501953, 48.0, 22.0], "text": "replace"}}, {"box": {"id": "obj-519", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [2268.5712933540344, 502.8571128845215, 179.0, 22.0], "text": "buffer~ Synthgrain-Buffer 10000"}}, {"box": {"id": "obj-520", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [2679.999840259552, 1002.8570830821991, 202.0, 22.0], "text": "buffer~ SynthgrainEnv @samps 512"}}, {"box": {"id": "obj-521", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [2657.142698764801, 665.7142460346222, 123.0, 22.0], "text": "peek~ SynthgrainEnv"}}, {"box": {"id": "obj-522", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2657.142698764801, 622.8571057319641, 57.0, 22.0], "text": "pack 0 0."}}, {"box": {"id": "obj-527", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [2691.4284110069275, 577.1428227424622, 302.0, 22.0], "text": "expr exp(-0.5*pow(($i1-((512-1)/2))/(0.4*((512-1)/2))\\,2))"}}, {"box": {"id": "obj-529", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 3, "outlettype": ["bang", "bang", "int"], "patching_rect": [2628.5712718963623, 534.2856824398041, 47.0, 22.0], "text": "uzi 512"}}, {"box": {"id": "obj-531", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [2867.0712599754333, 1091.4285063743591, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-533", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [1836.363570690155, 1298.9898200035095, 47.0, 136.0], "presentation": 1, "presentation_rect": [590.0588480234146, 510.81077671051025, 48.0, 201.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[3]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~[16]", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[1]"}}, {"box": {"id": "obj-41", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [2033.3474278450012, 88.05969834327698, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 9, "outlettype": ["int", "int", "float", "float", "float", "", "int", "float", ""], "patching_rect": [2033.3474278450012, 135.82089066505432, 156.0, 22.0], "text": "transport @clocksource live"}}, {"box": {"id": "obj-35", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1790.6976103782654, 194.1860395669937, 29.5, 22.0], "text": "1"}}, {"box": {"id": "obj-30", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1790.6976103782654, 144.18604135513306, 24.0, 24.0]}}, {"box": {"id": "obj-28", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1840.7081127166748, 221.23895585536957, 99.0, 22.0], "text": "0, 1 100 0 10000"}}, {"box": {"id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [1840.697608590126, 254.65115368366241, 34.0, 22.0], "text": "line~"}}, {"box": {"id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1797.818118095398, 381.25, 34.0, 22.0], "text": "*~ 0."}}, {"box": {"buffername": "Synthgrain-<PERSON><PERSON>er", "id": "obj-3", "maxclass": "waveform~", "numinlets": 5, "numoutlets": 6, "outlettype": ["float", "float", "float", "float", "list", ""], "patching_rect": [1284.934567809105, 859.8130774497986, 256.0, 64.0], "presentation": 1, "presentation_rect": [5.454545259475708, 290.7216331958771, 195.8762776851654, 81.4432944059372], "waveformcolor": [1.0, 1.0, 1.0, 1.0]}}, {"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1771.875, 457.14282989501953, 146.0, 22.0], "text": "record~ <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1727.2726655006409, 1135.3534622192383, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-6", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1781.818118095398, 1248.0807309150696, 50.0, 35.0], "text": "midinote 60 60"}}, {"box": {"id": "obj-165", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1727.2726655006409, 1182.626187801361, 99.0, 22.0], "text": "steal 1, parallel 1"}}, {"box": {"id": "obj-166", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1836.363570690155, 1053.5352833271027, 29.5, 22.0], "text": "60"}}, {"box": {"id": "obj-185", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1727.2726655006409, 1248.0807309150696, 50.0, 35.0], "text": "midinote 60 60"}}, {"box": {"id": "obj-186", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1836.363570690155, 1135.3534622192383, 34.0, 22.0], "text": "pack"}}, {"box": {"id": "obj-187", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1836.363570690155, 1009.898921251297, 24.0, 24.0]}}, {"box": {"id": "obj-188", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [1836.363570690155, 1098.989827156067, 101.0, 22.0], "text": "makenote 60 100"}}, {"box": {"id": "obj-189", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1836.363570690155, 1182.626187801361, 87.0, 22.0], "text": "midinote $1 $1"}}, {"box": {"id": "obj-194", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [1836.363570690155, 1248.0807309150696, 150.0, 22.0], "text": "poly~ grainGen 16 args #0"}}, {"box": {"angle": 270.0, "bordercolor": [1.0, 1.0, 1.0, 1.0], "grad1": [0.388235294117647, 0.388235294117647, 0.388235294117647, 1.0], "grad2": [0.149019607843137, 0.149019607843137, 0.149019607843137, 1.0], "id": "obj-44", "maxclass": "panel", "mode": 1, "numinlets": 1, "numoutlets": 0, "patching_rect": [848.05, 335.06, 128.0, 128.0], "presentation": 1, "presentation_rect": [-2.133088111877441, -2.597402572631836, 728.57142162323, 737.6623306274414], "proportion": 0.5}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-25", 1], "source": ["obj-109", 0]}}, {"patchline": {"destination": ["obj-687", 1], "source": ["obj-11", 1]}}, {"patchline": {"destination": ["obj-687", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-115", 1]}}, {"patchline": {"destination": ["obj-73", 1], "source": ["obj-116", 1]}}, {"patchline": {"destination": ["obj-109", 0], "source": ["obj-117", 0]}}, {"patchline": {"destination": ["obj-117", 0], "source": ["obj-119", 0]}}, {"patchline": {"destination": ["obj-117", 1], "source": ["obj-120", 0]}}, {"patchline": {"destination": ["obj-117", 2], "source": ["obj-121", 0]}}, {"patchline": {"destination": ["obj-117", 3], "source": ["obj-122", 0]}}, {"patchline": {"destination": ["obj-117", 4], "source": ["obj-123", 0]}}, {"patchline": {"destination": ["obj-117", 5], "source": ["obj-124", 0]}}, {"patchline": {"destination": ["obj-117", 6], "source": ["obj-125", 0]}}, {"patchline": {"destination": ["obj-117", 7], "source": ["obj-126", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-129", 0]}}, {"patchline": {"destination": ["obj-74", 1], "source": ["obj-138", 1]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-138", 0]}}, {"patchline": {"destination": ["obj-129", 1], "source": ["obj-150", 0]}}, {"patchline": {"destination": ["obj-129", 2], "order": 0, "source": ["obj-151", 0]}}, {"patchline": {"destination": ["obj-221", 1], "order": 1, "source": ["obj-151", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-152", 0]}}, {"patchline": {"destination": ["obj-40", 1], "source": ["obj-153", 0]}}, {"patchline": {"destination": ["obj-119", 0], "source": ["obj-156", 0]}}, {"patchline": {"destination": ["obj-120", 0], "source": ["obj-157", 0]}}, {"patchline": {"destination": ["obj-121", 0], "source": ["obj-158", 0]}}, {"patchline": {"destination": ["obj-122", 0], "source": ["obj-159", 0]}}, {"patchline": {"destination": ["obj-123", 0], "source": ["obj-160", 0]}}, {"patchline": {"destination": ["obj-124", 0], "source": ["obj-161", 0]}}, {"patchline": {"destination": ["obj-125", 0], "source": ["obj-162", 0]}}, {"patchline": {"destination": ["obj-126", 0], "source": ["obj-163", 0]}}, {"patchline": {"destination": ["obj-194", 0], "source": ["obj-165", 0]}}, {"patchline": {"destination": ["obj-188", 0], "source": ["obj-166", 0]}}, {"patchline": {"destination": ["obj-218", 0], "source": ["obj-169", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-171", 0], "order": 1, "source": ["obj-170", 0]}}, {"patchline": {"destination": ["obj-176", 0], "order": 1, "source": ["obj-170", 3]}}, {"patchline": {"destination": ["obj-182", 0], "order": 0, "source": ["obj-170", 0]}}, {"patchline": {"destination": ["obj-190", 1], "order": 0, "source": ["obj-170", 3]}}, {"patchline": {"destination": ["obj-181", 0], "source": ["obj-171", 0]}}, {"patchline": {"destination": ["obj-170", 0], "source": ["obj-172", 0]}}, {"patchline": {"destination": ["obj-172", 0], "source": ["obj-174", 0]}}, {"patchline": {"destination": ["obj-191", 0], "source": ["obj-176", 0]}}, {"patchline": {"destination": ["obj-170", 0], "midpoints": [1431.5, 539.2997132992746, 1289.9343546032906, 539.2997132992746, 1289.9343546032906, 369.1491796231271, 1316.5, 369.1491796231271], "source": ["obj-178", 0]}}, {"patchline": {"destination": ["obj-187", 0], "order": 5, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-19", 0], "order": 4, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-20", 0], "order": 3, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-21", 0], "order": 0, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-31", 0], "order": 2, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-34", 0], "order": 1, "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-174", 0], "midpoints": [1252.5, 504.0846641516687, 1285.095644891262, 504.0846641516687, 1285.095644891262, 254.63304977178586, 1316.5, 254.63304977178586], "source": ["obj-180", 0]}}, {"patchline": {"destination": ["obj-193", 0], "source": ["obj-181", 0]}}, {"patchline": {"destination": ["obj-183", 0], "source": ["obj-182", 0]}}, {"patchline": {"destination": ["obj-18", 0], "order": 1, "source": ["obj-183", 0]}}, {"patchline": {"destination": ["obj-227", 0], "order": 0, "source": ["obj-183", 0]}}, {"patchline": {"destination": ["obj-189", 0], "source": ["obj-186", 0]}}, {"patchline": {"destination": ["obj-166", 0], "source": ["obj-187", 0]}}, {"patchline": {"destination": ["obj-186", 0], "source": ["obj-188", 0]}}, {"patchline": {"destination": ["obj-185", 1], "order": 2, "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-194", 0], "order": 0, "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-6", 1], "order": 1, "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-483", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-224", 0], "source": ["obj-191", 0]}}, {"patchline": {"destination": ["obj-169", 0], "order": 2, "source": ["obj-193", 0]}}, {"patchline": {"destination": ["obj-68", 0], "midpoints": [925.587245464325, 837.7794751434121, 1567.0472422242165, 837.7794751434121], "order": 0, "source": ["obj-193", 0]}}, {"patchline": {"destination": ["obj-71", 0], "midpoints": [925.587245464325, 837.5811590636149, 1261.5100367069244, 837.5811590636149], "order": 1, "source": ["obj-193", 0]}}, {"patchline": {"destination": ["obj-533", 0], "source": ["obj-194", 1]}}, {"patchline": {"destination": ["obj-533", 1], "source": ["obj-194", 0]}}, {"patchline": {"destination": ["obj-68", 0], "order": 0, "source": ["obj-198", 0]}}, {"patchline": {"destination": ["obj-71", 0], "order": 1, "source": ["obj-198", 0]}}, {"patchline": {"destination": ["obj-172", 1], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-481", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-129", 0], "source": ["obj-208", 0]}}, {"patchline": {"destination": ["obj-507", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-129", 0], "source": ["obj-211", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-215", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-216", 0]}}, {"patchline": {"destination": ["obj-208", 0], "source": ["obj-218", 0]}}, {"patchline": {"destination": ["obj-222", 0], "source": ["obj-218", 1]}}, {"patchline": {"destination": ["obj-129", 0], "source": ["obj-221", 0]}}, {"patchline": {"destination": ["obj-223", 0], "source": ["obj-222", 0]}}, {"patchline": {"destination": ["obj-211", 0], "source": ["obj-223", 1]}}, {"patchline": {"destination": ["obj-221", 0], "source": ["obj-223", 0]}}, {"patchline": {"destination": ["obj-178", 0], "order": 0, "source": ["obj-224", 1]}}, {"patchline": {"destination": ["obj-180", 0], "order": 1, "source": ["obj-224", 1]}}, {"patchline": {"destination": ["obj-225", 0], "source": ["obj-224", 0]}}, {"patchline": {"destination": ["obj-30", 0], "midpoints": [1356.5, 631.7023184895515, 1195.7294209599495, 631.7023184895515, 1195.7294209599495, 133.18604135513306, 1800.1976103782654, 133.18604135513306], "source": ["obj-225", 0]}}, {"patchline": {"destination": ["obj-139", 0], "source": ["obj-226", 0]}}, {"patchline": {"destination": ["obj-228", 0], "source": ["obj-227", 0]}}, {"patchline": {"destination": ["obj-230", 0], "source": ["obj-228", 2]}}, {"patchline": {"destination": ["obj-230", 0], "source": ["obj-228", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-18", 0], "order": 1, "source": ["obj-230", 0]}}, {"patchline": {"destination": ["obj-231", 0], "order": 0, "source": ["obj-230", 0]}}, {"patchline": {"destination": ["obj-232", 0], "order": 0, "source": ["obj-231", 0]}}, {"patchline": {"destination": ["obj-234", 0], "order": 1, "source": ["obj-231", 0]}}, {"patchline": {"destination": ["obj-233", 0], "order": 0, "source": ["obj-232", 0]}}, {"patchline": {"destination": ["obj-235", 0], "order": 1, "source": ["obj-232", 0]}}, {"patchline": {"destination": ["obj-236", 0], "source": ["obj-233", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-234", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-235", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-236", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-23", 1], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-35", 0], "order": 1, "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-36", 0], "order": 0, "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-483", 1], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-156", 0], "order": 7, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-157", 0], "order": 6, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-158", 0], "order": 5, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-159", 0], "order": 4, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-160", 0], "order": 3, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-161", 0], "order": 2, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-162", 0], "order": 1, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-163", 0], "order": 0, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-481", 1], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-1", 0], "order": 0, "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-174", 0], "midpoints": [1800.1976103782654, 228.16755103349692, 1316.5, 228.16755103349692], "order": 1, "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-28", 0], "order": 1, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-469", 0], "source": ["obj-36", 1]}}, {"patchline": {"destination": ["obj-48", 0], "order": 0, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-165", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-150", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-151", 0], "source": ["obj-40", 1]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-450", 0], "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-467", 0], "source": ["obj-442", 0]}}, {"patchline": {"destination": ["obj-466", 0], "source": ["obj-450", 0]}}, {"patchline": {"destination": ["obj-519", 0], "midpoints": [2435.2141411304474, 492.71328711509705, 2278.0712933540344, 492.71328711509705], "source": ["obj-469", 0]}}, {"patchline": {"destination": ["obj-476", 0], "midpoints": [2504.166522423426, 657.4793360233307, 2503.7855656147003, 657.4793360233307], "source": ["obj-478", 6]}}, {"patchline": {"destination": ["obj-478", 0], "source": ["obj-479", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-490", 0], "source": ["obj-481", 1]}}, {"patchline": {"destination": ["obj-513", 0], "source": ["obj-481", 0]}}, {"patchline": {"destination": ["obj-491", 0], "source": ["obj-483", 1]}}, {"patchline": {"destination": ["obj-515", 0], "source": ["obj-483", 0]}}, {"patchline": {"destination": ["obj-45", 0], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-494", 0], "source": ["obj-490", 0]}}, {"patchline": {"destination": ["obj-495", 0], "source": ["obj-491", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-496", 0], "source": ["obj-507", 0]}}, {"patchline": {"destination": ["obj-497", 0], "source": ["obj-513", 0]}}, {"patchline": {"destination": ["obj-504", 0], "source": ["obj-515", 0]}}, {"patchline": {"destination": ["obj-516", 0], "source": ["obj-517", 0]}}, {"patchline": {"destination": ["obj-519", 0], "midpoints": [2343.7855751514435, 493.41328711509686, 2278.0712933540344, 493.41328711509686], "source": ["obj-518", 0]}}, {"patchline": {"destination": ["obj-479", 0], "source": ["obj-519", 1]}}, {"patchline": {"destination": ["obj-49", 1], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-521", 0], "source": ["obj-522", 0]}}, {"patchline": {"destination": ["obj-522", 1], "source": ["obj-527", 0]}}, {"patchline": {"destination": ["obj-522", 0], "order": 1, "source": ["obj-529", 2]}}, {"patchline": {"destination": ["obj-527", 0], "midpoints": [2666.0712718963623, 567.3928061485294, 2700.9284110069275, 567.3928061485294], "order": 0, "source": ["obj-529", 2]}}, {"patchline": {"destination": ["obj-517", 0], "source": ["obj-531", 0]}}, {"patchline": {"destination": ["obj-11", 1], "order": 1, "source": ["obj-533", 1]}}, {"patchline": {"destination": ["obj-11", 0], "order": 1, "source": ["obj-533", 0]}}, {"patchline": {"destination": ["obj-687", 1], "order": 0, "source": ["obj-533", 1]}}, {"patchline": {"destination": ["obj-687", 0], "order": 0, "source": ["obj-533", 0]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-65", 0]}}, {"patchline": {"destination": ["obj-65", 1], "source": ["obj-66", 0]}}, {"patchline": {"destination": ["obj-66", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-72", 0], "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-69", 1], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-70", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-115", 0], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-116", 0], "source": ["obj-72", 1]}}, {"patchline": {"destination": ["obj-138", 1], "order": 1, "source": ["obj-73", 1]}}, {"patchline": {"destination": ["obj-138", 0], "order": 1, "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-74", 1], "order": 0, "source": ["obj-73", 1]}}, {"patchline": {"destination": ["obj-74", 0], "order": 0, "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-9", 0]}}], "originid": "pat-458", "parameters": {"obj-11::obj-23": ["bypass", "bypass", 0], "obj-11::obj-28": ["Size", "Size", 0], "obj-11::obj-3": ["Regen", "Regen", 0], "obj-11::obj-60": ["<PERSON><PERSON>", "<PERSON><PERSON>", 0], "obj-11::obj-62": ["Dry", "Dry", 0], "obj-11::obj-63": ["Early", "Early", 0], "obj-11::obj-64": ["Tail", "Tail", 0], "obj-11::obj-65": ["Spread", "Spread", 0], "obj-11::obj-66": ["Time", "Time", 0], "obj-138::obj-23": ["bypass[1]", "bypass", 0], "obj-138::obj-28": ["Size[1]", "Size", 0], "obj-138::obj-3": ["Regen[1]", "Regen", 0], "obj-138::obj-60": ["Damp[1]", "<PERSON><PERSON>", 0], "obj-138::obj-62": ["Dry[1]", "Dry", 0], "obj-138::obj-63": ["Early[1]", "Early", 0], "obj-138::obj-64": ["Tail[1]", "Tail", 0], "obj-138::obj-65": ["Spread[1]", "Spread", 0], "obj-138::obj-66": ["Time[1]", "Time", 0], "obj-171": ["live.grid", "live.grid", 0], "obj-182": ["live.grid[1]", "live.grid", 0], "obj-2": ["Speed", "Speed", 0], "obj-533": ["live.gain~[3]", "live.gain~[16]", 0], "obj-73": ["live.gain~[2]", "live.gain~[2]", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "parameter_overrides": {"obj-138::obj-23": {"parameter_longname": "bypass[1]"}, "obj-138::obj-28": {"parameter_longname": "Size[1]"}, "obj-138::obj-3": {"parameter_longname": "Regen[1]"}, "obj-138::obj-60": {"parameter_longname": "Damp[1]"}, "obj-138::obj-62": {"parameter_longname": "Dry[1]"}, "obj-138::obj-63": {"parameter_longname": "Early[1]"}, "obj-138::obj-64": {"parameter_longname": "Tail[1]"}, "obj-138::obj-65": {"parameter_longname": "Spread[1]"}, "obj-138::obj-66": {"parameter_longname": "Time[1]"}}, "inherited_shortname": 1}, "dependency_cache": [{"name": "bp.Gigaverb.maxpat", "bootpath": "C74:/packages/BEAP/clippings/BEAP/Effects", "type": "JSON", "implicit": 1}, {"name": "grainGen.maxpat", "bootpath": "~/Documents/2024/Gummy/Portfolio 2/Looper", "patcherrelativepath": ".", "type": "JSON", "implicit": 1}, {"name": "pan2.maxpat", "bootpath": "~/Library/Application Support/Cycling '74/Max 9/Examples/spatialization/panning/lib", "patcherrelativepath": "../../../../../Library/Application Support/Cycling '74/Max 9/Examples/spatialization/panning/lib", "type": "JSON", "implicit": 1}], "autosave": 0}}