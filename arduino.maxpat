{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 77.0, 1624.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-36", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [525.8823748826981, 599.4117897152901, 50.0, 22.0]}}, {"box": {"id": "obj-34", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [637.0588501095772, 599.4117897152901, 50.0, 22.0]}}, {"box": {"id": "obj-32", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [581.764730155468, 599.4117897152901, 50.0, 22.0]}}, {"box": {"id": "obj-30", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [470.00001960992813, 599.4117897152901, 50.0, 22.0]}}, {"box": {"id": "obj-28", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [412.94119369983673, 599.4117897152901, 50.0, 22.0]}}, {"box": {"id": "obj-26", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [356.47060310840607, 599.4117897152901, 50.0, 22.0]}}, {"box": {"id": "obj-22", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [468.0, 105.8823573589325, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-20", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [374.7058979868889, 234.7058921456337, 39.0, 22.0], "text": "port z"}}, {"box": {"id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [389.4117809534073, 159.41177135705948, 50.0, 22.0], "text": "select 0"}}, {"box": {"id": "obj-17", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [420.4117809534073, 234.7058921456337, 39.0, 22.0], "text": "port c"}}, {"box": {"id": "obj-14", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [534.8823748826981, 263.52942276000977, 32.0, 22.0], "text": "print"}}, {"box": {"id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [468.0, 263.52942276000977, 49.0, 22.0], "text": "metro 1"}}, {"box": {"id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 6, "outlettype": ["float", "float", "float", "float", "float", "float"], "patching_rect": [468.2353136539459, 528.8235514760017, 87.0, 22.0], "text": "unpack f f f f f f"}}, {"box": {"id": "obj-9", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [468.0, 487.6470791697502, 71.0, 22.0], "text": "fromsymbol"}}, {"box": {"id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [468.0, 446.4706068634987, 40.0, 22.0], "text": "itoa"}}, {"box": {"id": "obj-7", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [468.0, 406.47060519456863, 75.0, 22.0], "text": "zl.group 100"}}, {"box": {"id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [468.0, 360.0000150203705, 73.0, 22.0], "text": "select 13 10"}}, {"box": {"id": "obj-3", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [621.1764965057373, 206.47059684991837, 24.0, 24.0]}}, {"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["int", ""], "patching_rect": [468.0, 319.0, 89.0, 22.0], "text": "serial c 115200"}}], "lines": [{"patchline": {"destination": ["obj-6", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-10", 1]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-10", 2]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-10", 4]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-10", 5]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-10", 3]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 1]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-11", 0], "order": 0, "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-18", 0], "order": 1, "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-6", 2]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-9", 0]}}], "originid": "pat-5", "dependency_cache": [], "autosave": 0}}