{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [172.0, 140.0, 1213.0, 811.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-86", "maxclass": "live.scope~", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [459.0361615419388, 3167.469996571541, 184.0, 68.0]}}, {"box": {"id": "obj-85", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [356.0, 2689.0, 29.5, 22.0], "text": "1"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-69", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["signal", "signal", "", ""], "patching_rect": [503.0, 3016.0, 223.0, 22.0], "text": "adsr~"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-70", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [723.0, 2962.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-71", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [672.0, 2962.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-72", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [621.0, 2962.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-73", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [569.0, 2962.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-76", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [747.0, 2917.0, 57.0, 22.0], "text": "zl nth 10"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-77", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [682.0, 2917.0, 50.0, 22.0], "text": "zl nth 5"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-78", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [617.0, 2917.0, 62.0, 22.0], "text": "zl nth 6"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-79", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [552.0, 2917.0, 62.0, 22.0], "text": "zl nth 4"}}, {"box": {"id": "obj-81", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [552.0, 2671.0, 24.0, 24.0]}}, {"box": {"addpoints": [0.0, 0.0, 1, 0.0, 1.0, 0, 697.6417658176828, 0.0, 0, 928.3518486834587, 0.000321238835653, 0, 1000.0, 0.0, 1], "classic_curve": 1, "clickadd": 0, "clicksustain": 0, "id": "obj-83", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "outputmode": 1, "parameter_enable": 0, "patching_rect": [552.0, 2726.506124854088, 200.0, 100.0]}}, {"box": {"id": "obj-62", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [191.5, 2918.0, 45.0, 45.0]}}, {"box": {"id": "obj-232", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["signal", "signal", "", ""], "patching_rect": [8.0, 2774.0, 135.0, 22.0], "text": "adsr~ @maxsustain 0.9"}}, {"box": {"id": "obj-231", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [0.0, 2721.0, 150.0, 20.0], "text": "adsr "}}, {"box": {"id": "obj-228", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [991.0, 2888.0, 116.0, 22.0], "text": "rampsmooth~ 25 25"}}, {"box": {"format": 6, "id": "obj-172", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [382.0, 2601.0, 50.0, 22.0]}}, {"box": {"id": "obj-154", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [381.0, 2637.0, 81.0, 22.0], "text": "setdomain $1"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [199.0, 2645.0, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-38", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [259.0, 2178.0, 39.0, 22.0], "text": "/ 127."}}, {"box": {"id": "obj-169", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [396.0, 2513.0, 80.0, 22.0], "text": "loadmess 84."}}, {"box": {"id": "obj-168", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [300.0, 2513.0, 93.0, 22.0], "text": "loadmess 3096."}}, {"box": {"id": "obj-164", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [225.0, 2513.0, 73.0, 22.0], "text": "loadmess 1."}}, {"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [203.0, 2462.0, 32.0, 22.0], "text": "mtof"}}, {"box": {"format": 6, "id": "obj-41", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [300.0, 2555.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-49", "maxclass": "flonum", "maximum": 100.0, "minimum": 1.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [356.0, 2555.0, 52.0, 23.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-18", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [246.0, 2555.0, 52.0, 23.0]}}, {"box": {"id": "obj-50", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [199.0, 2598.0, 110.38960933685303, 22.0], "text": "reson~"}}, {"box": {"id": "obj-51", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [344.0, 2636.0, 24.0, 24.0]}}, {"box": {"id": "obj-52", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [991.0, 2848.0, 34.0, 22.0], "text": "line~"}}, {"box": {"addpoints": [0.0, 0.0, 0, 18.822406200652427, 1.0, 0, 367.3345332450055, 0.138822566668193, 0, 1080.7263026846217, 0.044704915682475, 0, 2000.0, 0.0, 0], "classic_curve": 1, "domain": 2000.0, "id": "obj-28", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [931.0, 2712.0, 200.0, 100.0]}}, {"box": {"id": "obj-53", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [199.0, 2699.0, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-54", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [190.0, 2744.0, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[1]"}}, {"box": {"format": 6, "id": "obj-55", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [0.0, 2523.0, 50.0, 22.0]}}, {"box": {"id": "obj-56", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [199.0, 2555.0, 43.0, 22.0], "text": "cycle~"}}, {"box": {"id": "obj-202", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [301.0, 1473.0, 123.0, 22.0], "text": "scale 1. 0. 150. 4000."}}, {"box": {"id": "obj-201", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [155.0, 1092.0, 50.0, 22.0]}}, {"box": {"id": "obj-199", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [155.0, 1139.0, 81.0, 22.0], "text": "setdomain $1"}}, {"box": {"id": "obj-194", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [118.0, 1138.0, 24.0, 24.0]}}, {"box": {"format": 6, "id": "obj-192", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [201.0, 1438.0, 50.0, 22.0]}}, {"box": {"id": "obj-190", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [201.0, 1405.0, 64.0, 22.0], "text": "snapshot~"}}, {"box": {"id": "obj-189", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [56.0, 1365.0, 106.0, 22.0], "text": "metro 1 @active 1"}}, {"box": {"id": "obj-188", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [201.0, 1365.0, 58.0, 22.0], "text": "curve~ 0."}}, {"box": {"addpoints_with_curve": [0.0, 0.029901056326412, 0, 0.0, 489.8060198918813, 0.714690983055835, 0, -0.15, 1336.9940471341802, 0.558371928856499, 0, 0.0, 5095.000000000003, 0.0, 0, -0.4], "classic_curve": 1, "domain": 5095.0, "id": "obj-187", "maxclass": "function", "mode": 1, "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [131.0, 1208.0, 228.3950799703598, 130.86420798301697]}}, {"box": {"id": "obj-185", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [240.0, 1518.0, 50.0, 22.0]}}, {"box": {"id": "obj-143", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [326.0, 2050.0, 50.0, 22.0]}}, {"box": {"id": "obj-123", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [254.0, 2065.0, 24.0, 24.0]}}, {"box": {"id": "obj-121", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [254.0, 2115.0, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-101", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [397.0, 1824.0, 24.0, 24.0]}}, {"box": {"id": "obj-99", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [397.0, 1857.0, 59.0, 22.0], "text": "random 3"}}, {"box": {"id": "obj-98", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [397.0, 1897.0, 50.0, 22.0]}}, {"box": {"id": "obj-96", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [279.0, 1938.0, 30.0, 22.0], "text": "* 12"}}, {"box": {"id": "obj-92", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [262.0, 1971.0, 29.5, 22.0], "text": "+"}}, {"box": {"id": "obj-82", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [262.0, 1857.0, 24.0, 24.0]}}, {"box": {"id": "obj-80", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [262.0, 1897.0, 129.0, 22.0], "text": "random @range 84 96"}}, {"box": {"id": "obj-75", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [147.0, 1875.0, 31.111112594604492, 22.0], "text": "int"}}, {"box": {"id": "obj-74", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [202.0, 2000.0, 50.0, 22.0], "text": "104"}}, {"box": {"format": 6, "id": "obj-57", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [240.0, 1603.0, 50.0, 22.0]}}, {"box": {"id": "obj-31", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [202.0, 1868.0, 24.0, 24.0]}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [202.0, 1818.0, 34.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-58", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [366.0, 1697.0, 50.0, 22.0]}}, {"box": {"id": "obj-59", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [203.0, 1739.0, 182.8828827738762, 22.0], "text": "<"}}, {"box": {"id": "obj-16", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [204.0, 1602.0, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-14", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [256.0, 1631.0, 150.0, 20.0], "text": "wind velocity"}}, {"box": {"id": "obj-60", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [203.0, 1656.0, 56.0, 22.0], "text": "metro 50"}}, {"box": {"id": "obj-61", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [203.0, 1693.0, 73.0, 22.0], "text": "random 100"}}, {"box": {"channels": 1, "id": "obj-37", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 1, "numoutlets": 4, "orientation": 1, "outlettype": ["signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [558.0, 996.0, 136.0, 30.0], "saved_attribute_attributes": {"valueof": {"parameter_initial": [-18], "parameter_initial_enable": 1, "parameter_longname": "live.gain~[2]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "showname": 0, "varname": "live.gain~"}}, {"box": {"id": "obj-36", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [550.25, 1057.0, 45.0, 45.0]}}, {"box": {"id": "obj-33", "maxclass": "scope~", "numinlets": 2, "numoutlets": 0, "patching_rect": [389.0, 854.0, 130.0, 130.0]}}, {"box": {"id": "obj-30", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [613.0, 854.0, 50.0, 22.0]}}, {"box": {"id": "obj-27", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [558.0, 935.5, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [613.0, 895.0, 43.0, 22.0], "text": "cycle~"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["signal", "signal", "", ""], "patching_rect": [558.0, 746.0, 223.0, 22.0], "text": "adsr~"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-19", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [762.0, 695.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-20", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [711.0, 695.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-21", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [660.0, 695.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-22", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [609.0, 695.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [786.0, 650.0, 50.0, 22.0], "text": "zl nth 8"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [721.0, 650.0, 62.0, 22.0], "text": "zl nth 5"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [656.0, 650.0, 62.0, 22.0], "text": "zl nth 6"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [591.0, 650.0, 62.0, 22.0], "text": "zl nth 4"}}, {"box": {"id": "obj-12", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [591.0, 404.0, 24.0, 24.0]}}, {"box": {"addpoints": [0.0, 0.0, 1, 223.40425531914894, 0.866666666666667, 0, 643.6170212765958, 0.48, 0, 1000.0, 0.0, 1], "classic_curve": 1, "clickadd": 0, "clicksustain": 0, "id": "obj-6", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "outputmode": 1, "parameter_enable": 0, "patching_rect": [591.0, 458.0, 200.0, 100.0]}}, {"box": {"addpoints": [0.0, 0.0, 1, 265.9574468085106, 1.0, 0, 420.21276595744683, 0.773333333333333, 0, 632.9787234042553, 0.773333333333333, 0, 1000.0, 0.0, 1], "classic_curve": 1, "clickadd": 0, "clicksustain": 0, "id": "obj-5", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "outputmode": 1, "parameter_enable": 0, "patching_rect": [1056.0, 572.0, 200.0, 100.0]}}, {"box": {"addpoints_with_curve": [0.0, 0.0, 1, 0.0, 196.80851063829786, 1.0, 0, 0.0, 462.7659574468085, 0.546666666666667, 0, 0.0, 1000.0, 0.0, 0, 0.0], "classic_curve": 1, "clickadd": 0, "clicksustain": 0, "id": "obj-2", "maxclass": "function", "mode": 1, "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "outputmode": 1, "parameter_enable": 0, "patching_rect": [625.0, 248.0, 200.0, 100.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-48", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [425.0, 485.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-47", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [374.0, 485.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-46", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [323.0, 485.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-45", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [272.0, 485.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 4, "outlettype": ["", "", "", ""], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [1302.0, 354.0, 640.0, 480.0], "gridsize": [15.0, 15.0], "visible": 1, "boxes": [{"box": {"comment": "", "id": "obj-11", "index": 4, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [410.0, 197.0, 25.0, 25.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [410.0, 150.0, 62.0, 22.0], "text": "zl nth 10"}}, {"box": {"comment": "", "id": "obj-9", "index": 3, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [345.0, 197.0, 25.0, 25.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [345.0, 150.0, 62.0, 22.0], "text": "zl nth 5"}}, {"box": {"comment": "", "id": "obj-7", "index": 2, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [280.0, 197.0, 25.0, 25.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [280.0, 150.0, 62.0, 22.0], "text": "zl nth 6"}}, {"box": {"comment": "", "id": "obj-6", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [215.0, 197.0, 25.0, 25.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [215.0, 150.0, 62.0, 22.0], "text": "zl nth 4"}}, {"box": {"comment": "", "id": "obj-2", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [215.0, 102.0, 25.0, 25.0]}}], "lines": [{"patchline": {"destination": ["obj-9", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-10", 0], "midpoints": [224.5, 135.0, 354.5, 135.0], "order": 1, "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-12", 0], "midpoints": [224.5, 135.0, 419.5, 135.0], "order": 0, "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-3", 0], "order": 3, "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-8", 0], "midpoints": [224.5, 135.0, 289.5, 135.0], "order": 2, "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-8", 0]}}], "originid": "pat-432"}, "patching_rect": [272.0, 451.0, 172.0, 22.0], "saved_object_attributes": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "globalpatchername": ""}, "text": "p adsr values"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["signal", "signal", "", ""], "patching_rect": [221.0, 524.0, 223.0, 22.0], "text": "adsr~"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [245.0, 160.0, 63.0, 22.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-39", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [393.0, 382.0, 60.0, 22.0], "text": "listdump"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [871.0, 365.0, 559.0, 497.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-81", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [109.0, 400.0, 37.0, 22.0], "text": "t b l"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-72", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", "int"], "patching_rect": [356.0, 139.0, 68.0, 22.0], "text": "change 0."}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-68", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [356.0, 106.0, 59.0, 22.0], "text": "zl slice 1"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-12", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [87.0, 71.0, 153.0, 22.0], "text": "zl delace"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-61", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", "bang"], "patching_rect": [301.0, 272.0, 25.0, 22.0], "text": "b"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-60", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [316.0, 73.0, 59.0, 22.0], "text": "zl slice 2"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-59", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": [""], "patching_rect": [327.0, 330.0, 76.0, 22.0], "text": "pak 1 0. 1."}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-56", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [109.0, 332.0, 68.0, 22.0], "text": "delay 100"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-55", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [109.0, 368.0, 46.0, 22.0], "text": "zl reg"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", "int"], "patching_rect": [288.0, 140.0, 65.0, 22.0], "text": "change 0."}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-37", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", "bang"], "patching_rect": [136.0, 193.0, 22.0, 22.0], "text": "b"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-38", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", "int"], "patching_rect": [221.0, 140.0, 65.0, 22.0], "text": "change 0."}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [288.0, 106.0, 65.0, 22.0], "text": "zl nth 4"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [221.0, 106.0, 65.0, 22.0], "text": "zl nth 3"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-35", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", "int"], "patching_rect": [154.0, 140.0, 65.0, 22.0], "text": "change 0."}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["bang", "bang"], "patching_rect": [87.0, 193.0, 22.0, 22.0], "text": "b"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", "int"], "patching_rect": [87.0, 140.0, 65.0, 22.0], "text": "change 0."}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-30", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": [""], "patching_rect": [87.0, 274.0, 153.0, 22.0], "text": "pack 3 0. 0."}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": [""], "patching_rect": [136.0, 241.0, 171.0, 22.0], "text": "pack 2 0. 0."}}, {"box": {"comment": "", "id": "obj-14", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [107.0, 450.0, 25.0, 25.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [154.0, 106.0, 65.0, 22.0], "text": "zl nth 4"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-5", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [87.0, 106.0, 65.0, 22.0], "text": "zl nth 3"}}, {"box": {"comment": "", "id": "obj-6", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [87.0, 21.0, 25.0, 25.0]}}], "lines": [{"patchline": {"destination": ["obj-39", 0], "order": 0, "source": ["obj-12", 1]}}, {"patchline": {"destination": ["obj-4", 0], "order": 0, "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 1, "source": ["obj-12", 1]}}, {"patchline": {"destination": ["obj-5", 0], "order": 1, "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-55", 1], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-55", 1], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-29", 1], "order": 0, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-34", 0], "order": 1, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-30", 0], "order": 1, "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-56", 0], "order": 0, "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-30", 1], "order": 0, "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-37", 0], "order": 1, "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-29", 2], "order": 0, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-37", 0], "order": 1, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-29", 0], "order": 0, "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-56", 0], "order": 1, "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-30", 2], "order": 0, "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-34", 0], "order": 1, "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-81", 0], "source": ["obj-55", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-55", 1], "midpoints": [336.5, 356.0, 145.5, 356.0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-12", 0], "order": 1, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-60", 0], "midpoints": [96.5, 48.0, 325.5, 48.0], "order": 0, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-68", 0], "source": ["obj-60", 1]}}, {"patchline": {"destination": ["obj-56", 0], "midpoints": [310.5, 299.0, 118.5, 299.0], "source": ["obj-61", 0]}}, {"patchline": {"destination": ["obj-72", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-59", 1], "order": 0, "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-61", 0], "order": 1, "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-81", 1]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-81", 0]}}], "originid": "pat-434"}, "patching_rect": [333.0, 413.0, 77.0, 22.0], "saved_object_attributes": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "globalpatchername": ""}, "text": "p keep sust"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-9", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [245.0, 201.0, 54.0, 35.0], "text": "fix 0 1, fix 4 1"}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [212.0, 231.0, 20.0, 20.0]}}, {"box": {"addpoints": [0.0, 0.0, 1, 265.9574468085106, 1.0, 0, 638.2978723404256, 0.0, 0, 1000.0, 0.0, 0], "classic_curve": 0, "clicksustain": 0, "id": "obj-1", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "outputmode": 1, "parameter_enable": 0, "patching_rect": [212.0, 274.0, 200.0, 100.0]}}, {"box": {"attr": "mode", "id": "obj-3", "maxclass": "attrui", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [667.0, 196.0, 310.0, 22.0]}}, {"box": {"attr": "clickadd", "id": "obj-4", "maxclass": "attrui", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [633.0, 19.0, 150.0, 22.0]}}], "lines": [{"patchline": {"destination": ["obj-23", 0], "source": ["obj-1", 2]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-1", 3]}}, {"patchline": {"destination": ["obj-43", 0], "source": ["obj-1", 1]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-99", 0], "source": ["obj-101", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-121", 0]}}, {"patchline": {"destination": ["obj-121", 0], "source": ["obj-123", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-121", 1], "source": ["obj-143", 0]}}, {"patchline": {"destination": ["obj-60", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-164", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-168", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-169", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-154", 0], "source": ["obj-172", 0]}}, {"patchline": {"destination": ["obj-50", 1], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-185", 0]}}, {"patchline": {"destination": ["obj-188", 0], "source": ["obj-187", 1]}}, {"patchline": {"destination": ["obj-190", 0], "source": ["obj-188", 0]}}, {"patchline": {"destination": ["obj-190", 0], "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-24", 4], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-192", 0], "source": ["obj-190", 0]}}, {"patchline": {"destination": ["obj-202", 0], "source": ["obj-192", 0]}}, {"patchline": {"destination": ["obj-187", 0], "source": ["obj-194", 0]}}, {"patchline": {"destination": ["obj-187", 0], "source": ["obj-199", 0]}}, {"patchline": {"destination": ["obj-24", 3], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-199", 0], "source": ["obj-201", 0]}}, {"patchline": {"destination": ["obj-185", 0], "source": ["obj-202", 0]}}, {"patchline": {"destination": ["obj-24", 2], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-24", 1], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [342.5, 440.0, 203.0, 440.0, 203.0, 265.0, 221.5, 265.0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-27", 0], "order": 0, "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-33", 0], "order": 1, "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-27", 1], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-28", 1]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-36", 1], "order": 0, "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-36", 0], "order": 1, "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-32", 1], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-1", 0], "midpoints": [402.5, 413.0, 461.0, 413.0, 461.0, 267.0, 221.5, 267.0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-9", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-50", 2], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-45", 0], "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-46", 0], "source": ["obj-43", 1]}}, {"patchline": {"destination": ["obj-47", 0], "source": ["obj-43", 2]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-43", 3]}}, {"patchline": {"destination": ["obj-51", 0], "midpoints": [212.5, 2493.413528084755, 353.5, 2493.413528084755], "order": 0, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-56", 0], "order": 1, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-42", 1], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-42", 2], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-42", 3], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-42", 4], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-50", 3], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-81", 0], "order": 0, "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-85", 0], "order": 1, "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-228", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-54", 1], "order": 0, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-54", 0], "order": 1, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-62", 1], "order": 0, "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-62", 0], "order": 1, "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-60", 1], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-59", 1], "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-10", 0], "order": 1, "source": ["obj-6", 1]}}, {"patchline": {"destination": ["obj-13", 0], "order": 0, "source": ["obj-6", 1]}}, {"patchline": {"destination": ["obj-17", 0], "order": 3, "source": ["obj-6", 1]}}, {"patchline": {"destination": ["obj-8", 0], "order": 2, "source": ["obj-6", 1]}}, {"patchline": {"destination": ["obj-101", 0], "order": 0, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-123", 0], "order": 2, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-61", 0], "order": 3, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-82", 0], "order": 1, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-61", 0]}}, {"patchline": {"destination": ["obj-53", 1], "order": 1, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-86", 0], "order": 0, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-69", 4], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-69", 3], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-69", 2], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-69", 1], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-74", 0]}}, {"patchline": {"destination": ["obj-70", 0], "source": ["obj-76", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-77", 0]}}, {"patchline": {"destination": ["obj-72", 0], "source": ["obj-78", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-79", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-92", 0], "source": ["obj-80", 0]}}, {"patchline": {"destination": ["obj-83", 0], "source": ["obj-81", 0]}}, {"patchline": {"destination": ["obj-80", 0], "source": ["obj-82", 0]}}, {"patchline": {"destination": ["obj-76", 0], "order": 0, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-77", 0], "order": 1, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-78", 0], "order": 2, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-79", 0], "order": 3, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-81", 0], "midpoints": [742.5, 2836.506124854088, 761.6385582685471, 2836.506124854088, 761.6385582685471, 2661.0, 561.5, 2661.0], "source": ["obj-83", 3]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-85", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-9", 0]}}, {"patchline": {"destination": ["obj-74", 1], "source": ["obj-92", 0]}}, {"patchline": {"destination": ["obj-92", 1], "source": ["obj-96", 0]}}, {"patchline": {"destination": ["obj-96", 0], "source": ["obj-98", 0]}}, {"patchline": {"destination": ["obj-98", 0], "source": ["obj-99", 0]}}], "originid": "pat-430", "parameters": {"obj-37": ["live.gain~[2]", "live.gain~", 0], "obj-54": ["live.gain~", "live.gain~", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "inherited_shortname": 1}, "dependency_cache": [], "autosave": 0}}