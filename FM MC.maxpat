{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [35.0, 78.0, 1980.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-9", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [401.72649973630905, 1005.1282153129578, 80.0, 22.0], "text": "loadmess 0.1"}}, {"box": {"format": 6, "id": "obj-69", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [401.72649973630905, 1051.0, 50.0, 22.0]}}, {"box": {"id": "obj-70", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [438.0, 1153.0, 32.0, 22.0], "text": "tanh"}}, {"box": {"id": "obj-71", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [418.0, 1233.0, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-72", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [438.0, 1189.0, 29.5, 22.0], "text": "!/ 1."}}, {"box": {"id": "obj-73", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [378.0, 1120.0, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-74", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [378.0, 1160.0, 39.0, 22.0], "text": "tanh~"}}, {"box": {"id": "obj-64", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [259.0, 1150.0, 32.0, 22.0], "text": "tanh"}}, {"box": {"id": "obj-65", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [239.0, 1229.0, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-66", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [259.0, 1186.0, 29.5, 22.0], "text": "!/ 1."}}, {"box": {"id": "obj-1240", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [199.0, 1116.0, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-1230", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [199.0, 1157.0, 39.0, 22.0], "text": "tanh~"}}, {"box": {"id": "obj-34", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [715.3846226334572, 407.6923118233681, 77.0, 22.0], "text": "loadmess 10"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [636.4359034895897, 407.6923118233681, 70.0, 22.0], "text": "loadmess 1"}}, {"box": {"id": "obj-30", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [423.0769273638725, 458.11966276168823, 70.0, 22.0], "text": "loadmess 3"}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [181.19658303260803, 311.9658151268959, 73.0, 22.0], "text": "loadmess 2."}}, {"box": {"id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [264.9572676420212, 311.9658151268959, 83.0, 22.0], "text": "loadmess 200"}}, {"box": {"format": 6, "id": "obj-21", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [694.0171010494232, 467.5213722586632, 50.0, 22.0]}}, {"box": {"id": "obj-22", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": [""], "patching_rect": [597.4359034895897, 528.2222275137901, 97.0, 22.0], "text": "pak increment f f"}}, {"box": {"format": 6, "id": "obj-23", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [636.4359034895897, 467.5213722586632, 50.0, 22.0]}}, {"box": {"id": "obj-20", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [423.0769273638725, 513.6752188801765, 50.0, 22.0]}}, {"box": {"id": "obj-17", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [622.2222285270691, 596.5812026262283, 150.0, 20.0], "text": "Mdepth"}}, {"box": {"format": 6, "id": "obj-4", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [237.6068400144577, 362.39316606521606, 50.0, 22.0]}}, {"box": {"format": 6, "id": "obj-7", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [181.19658303260803, 360.68376433849335, 50.0, 22.0]}}, {"box": {"id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": [""], "patching_rect": [144.4444459080696, 417.9487221837044, 94.0, 22.0], "text": "pak harmonic f f"}}, {"box": {"format": 6, "id": "obj-2", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [537.6068430542946, 330.7692341208458, 50.0, 22.0]}}, {"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": [""], "patching_rect": [446.1538506746292, 386.3247902393341, 97.0, 22.0], "text": "pak increment f f"}}, {"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 3, "outlettype": ["multichannelsignal", "", ""], "patching_rect": [384.6153885126114, 896.5812056660652, 111.0, 22.0], "text": "mc.line~ @chans 8"}}, {"box": {"id": "obj-45", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [384.6153885126114, 860.6837694048882, 111.0, 22.0], "text": "spreadinclusive 0 1"}}, {"box": {"id": "obj-46", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [384.6153885126114, 817.0940253734589, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [263.6153885126114, 1005.1282153129578, 84.0, 22.0], "text": "mc.unpack~ 2"}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [263.6153885126114, 952.1367617845535, 140.0, 22.0], "text": "mc.stereo~ @autogain 1"}}, {"box": {"id": "obj-41", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [267.0, 1302.0, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_initial": [-20], "parameter_initial_enable": 1, "parameter_longname": "live.gain~[3]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[2]"}}, {"box": {"displaychan": 4, "id": "obj-38", "maxclass": "spectroscope~", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [608.5470147132874, 795.7265037894249, 300.0, 100.0]}}, {"box": {"id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [526.4957318305969, 659.8290665149689, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [416.72649973630905, 570.0854758620262, 108.0, 22.0], "text": "mc.sig~ @chans 8"}}, {"box": {"id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [395.72649973630905, 608.5470147132874, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-8", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [206.0, 1336.0, 31.0, 22.0], "text": "stop"}}, {"box": {"id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [597.4359034895897, 621.3675276637077, 108.0, 22.0], "text": "mc.sig~ @chans 8"}}, {"box": {"format": 6, "id": "obj-12", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [780.3418882489204, 541.8803473711014, 50.0, 22.0]}}, {"box": {"id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [395.72649973630905, 706.8376139998436, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-18", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [118.0, 1336.0, 72.0, 22.0], "text": "startwindow"}}, {"box": {"id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [206.0, 1439.0, 35.0, 22.0], "text": "dac~"}}, {"box": {"id": "obj-27", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [287.1794900894165, 697.4359045028687, 42.0, 22.0], "text": "mc.+~"}}, {"box": {"id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [287.1794900894165, 564.1025698184967, 108.0, 22.0], "text": "mc.sig~ @chans 8"}}, {"box": {"format": 6, "id": "obj-33", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [482.90598779916763, 327.35043066740036, 50.0, 22.0]}}, {"box": {"id": "obj-5", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [241.17826583981514, 759.1196658015251, 42.857148349285126, 20.0], "text": "Fc"}}, {"box": {"id": "obj-35", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [478.63248348236084, 516.2222275137901, 54.285721242427826, 34.0], "text": "Fm Factor"}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [287.1794900894165, 758.1196658015251, 120.0, 22.0], "text": "mc.cycle~ @chans 8"}}, {"box": {"id": "obj-37", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [395.72649973630905, 659.8290665149689, 62.0, 22.0], "text": "mc.cycle~"}}], "lines": [{"patchline": {"destination": ["obj-28", 1], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-1230", 0]}}, {"patchline": {"destination": ["obj-1230", 0], "source": ["obj-1240", 0]}}, {"patchline": {"destination": ["obj-27", 1], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-1", 2], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-22", 2], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-22", 1], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-6", 1], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-13", 1], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-27", 0], "order": 1, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-6", 0], "order": 0, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-1", 1], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-38", 0], "order": 0, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 1, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-1240", 0], "order": 1, "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-73", 0], "order": 0, "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-3", 2], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-26", 1], "source": ["obj-41", 1]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-40", 1], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-45", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-28", 0], "order": 0, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-37", 0], "order": 1, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-66", 0], "source": ["obj-64", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-65", 0]}}, {"patchline": {"destination": ["obj-65", 1], "source": ["obj-66", 0]}}, {"patchline": {"destination": ["obj-1240", 1], "order": 3, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-64", 0], "order": 2, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-70", 0], "order": 0, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-73", 1], "order": 1, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-3", 1], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-72", 0], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-41", 1], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-71", 1], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-74", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-9", 0]}}], "originid": "pat-20", "parameters": {"obj-41": ["live.gain~[3]", "live.gain~", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "inherited_shortname": 1}, "dependency_cache": [], "autosave": 0}}