{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [642.0, 107.0, 1000.0, 790.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-84", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [701.0, 41.0, 32.5, 22.0], "text": "10"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-71", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [580.0, 39.0, 32.5, 22.0], "text": "5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-45", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [365.0, 47.0, 32.5, 22.0], "text": "0.8"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-35", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [307.0, 50.0, 32.5, 22.0], "text": "30"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-16", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [243.0, 50.0, 32.5, 22.0], "text": "16"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-14", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [243.0, 25.0, 60.0, 22.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-70", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [876.0, 241.0, 32.5, 22.0], "text": "+ 0"}}, {"box": {"id": "obj-11", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [101.0, 117.0, 45.0, 45.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-7", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [15.0, 349.0, 351.0, 456.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-18", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [306.0, 226.0, 32.5, 18.0], "text": "17"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-14", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [288.0, 194.0, 32.5, 18.0], "text": "392"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-4", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [250.0, 189.0, 32.5, 18.0], "text": "29"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [250.0, 162.0, 60.0, 20.0], "text": "loadbang"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-17", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [266.0, 265.0, 50.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-15", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [249.0, 240.0, 50.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-13", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [234.0, 220.0, 50.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [227.0, 294.0, 59.5, 20.0], "text": "reson~"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [187.0, 315.0, 42.0, 20.0], "text": "*~ 0.5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-9", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [182.0, 349.0, 37.0, 20.0], "text": "dac~"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [186.0, 247.0, 32.5, 20.0], "text": "*~"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-7", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [146.0, 180.0, 46.0, 20.0], "text": "noise~"}}, {"box": {"comment": "", "id": "obj-6", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [201.0, 71.0, 25.0, 25.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-5", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [201.0, 180.0, 46.0, 20.0], "text": "line~ 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-2", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [201.0, 132.0, 77.0, 18.0], "text": "0, 0.3 1 0 20"}}], "lines": [{"patchline": {"destination": ["obj-14", 0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-18", 0], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-4", 0], "order": 2, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-9", 1], "order": 0, "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-9", 0], "order": 1, "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-11", 1], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-11", 2], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-11", 3], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-8", 1], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-8", 0]}}], "originid": "pat-2257"}, "patching_rect": [76.0, 656.0, 49.0, 22.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p noise"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-109", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [773.0, 182.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-104", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [761.0, 144.0, 57.0, 22.0], "text": "s redraw"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-97", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [819.0, 219.0, 57.0, 36.0], "text": "max $1, set 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-96", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [818.0, 255.0, 73.0, 22.0], "text": "counter"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-95", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": [""], "patching_rect": [766.0, 303.0, 76.0, 22.0], "text": "pack 0 0 0 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-94", "linecount": 3, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [745.0, 324.0, 81.0, 50.0], "text": "linesegment $1 $2 $3 $4 0 50 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-92", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [881.0, 213.0, 31.0, 22.0], "text": "r cb"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-89", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [917.0, 198.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-86", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [917.0, 221.0, 57.0, 36.0], "text": "max $1, set 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-82", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [898.0, 255.0, 73.0, 22.0], "text": "counter"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": [""], "patching_rect": [845.0, 350.0, 76.0, 22.0], "text": "pack 0 0 0 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-52", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [792.0, 372.0, 179.0, 22.0], "text": "linesegment $2 $1 $3 $4 0 50 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [451.0, 332.0, 55.0, 22.0], "text": "r redraw"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-38", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [946.0, 297.0, 32.5, 22.0], "text": "- 10"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [866.0, 325.0, 99.0, 22.0], "text": "scale 0 15 260 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [791.0, 280.0, 99.0, 22.0], "text": "scale 0. 5. 0 400"}}, {"box": {"bgtransparent": 1, "id": "obj-28", "maxclass": "lcd", "numinlets": 1, "numoutlets": 4, "outlettype": ["list", "list", "int", ""], "patching_rect": [443.0, 396.0, 470.0, 276.0], "presentation": 1, "presentation_rect": [423.0, 377.0, 470.0, 276.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [739.0, 56.0, 30.0, 22.0], "text": "r ys"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [556.0, 59.0, 30.0, 22.0], "text": "r xs"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [460.0, 689.0, 32.0, 22.0], "text": "s xs"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-22", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [407.0, 685.0, 32.0, 22.0], "text": "s ys"}}, {"box": {"floatoutput": 1, "id": "obj-19", "maxclass": "slider", "min": 0.1, "mult": 0.01, "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [442.0, 672.0, 468.0, 17.0], "size": 1500.0}}, {"box": {"floatoutput": 1, "id": "obj-18", "maxclass": "slider", "min": 0.1, "mult": 0.01, "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [426.0, 396.0, 17.0, 275.0], "size": 4000.01001}}, {"box": {"id": "obj-141", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [539.0, 171.0, 20.0, 20.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-128", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [539.0, 198.0, 33.0, 22.0], "text": "s cb"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-123", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "int", "int"], "patching_rect": [837.0, 181.0, 79.0, 22.0], "text": "unpack s 0 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-122", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [610.0, 93.0, 60.0, 20.0], "text": "x scope"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-120", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [791.0, 87.0, 52.0, 20.0], "text": "y scope"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-117", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [517.0, 362.0, 201.0, 22.0], "text": "clear, paintoval $1 $2 $3 $4 0 255 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-115", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [663.0, 306.0, 32.5, 22.0], "text": "- 5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-116", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [701.0, 306.0, 32.5, 22.0], "text": "+ 5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-114", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [522.0, 310.0, 32.5, 22.0], "text": "- 5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-113", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [555.0, 310.0, 32.5, 22.0], "text": "+ 5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-108", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [78.0, 61.0, 66.0, 22.0], "text": "sel 32 113"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-106", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 4, "outlettype": ["int", "int", "int", "int"], "patching_rect": [78.0, 38.0, 59.5, 22.0], "text": "key"}}, {"box": {"id": "obj-105", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [77.0, 583.0, 49.0, 49.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-103", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [139.0, 391.0, 22.0, 22.0], "text": "r r"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-102", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [466.0, 100.0, 24.0, 22.0], "text": "s r"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-101", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [141.0, 423.0, 32.5, 22.0], "text": "0"}}, {"box": {"floatoutput": 1, "id": "obj-99", "maxclass": "dial", "min": 0.01, "mult": 0.01, "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [403.0, 81.0, 40.0, 40.0], "size": 199.0}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-98", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [759.0, 224.0, 32.5, 22.0], "text": "- 10"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-93", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 7, "numoutlets": 1, "outlettype": [""], "patching_rect": [522.0, 337.0, 133.0, 22.0], "text": "pack 0. 0. 0. 0. 0 255 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-87", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [755.0, 276.0, 18.0, 20.0], "text": "y"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-85", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [656.0, 255.0, 33.0, 22.0], "triangle": 0}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-83", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [703.0, 276.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-77", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [740.0, 87.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-76", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [703.0, 251.0, 112.0, 22.0], "text": "scale 0. 15. 260. 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-75", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [703.0, 178.0, 24.0, 22.0], "text": "r y"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-66", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [351.0, 428.0, 26.0, 22.0], "text": "s y"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-56", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [564.0, 284.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-49", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [523.0, 284.0, 35.0, 22.0], "text": "% 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-47", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [643.0, 228.0, 35.0, 22.0], "triangle": 0}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-44", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [618.0, 283.0, 20.0, 20.0], "text": "x"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-37", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [474.0, 69.0, 37.0, 22.0], "text": "reset"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-34", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [523.0, 258.0, 112.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [610.0, 173.0, 45.0, 22.0], "text": "value l"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [610.0, 144.0, 67.0, 22.0], "text": "select size"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-21", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [556.0, 88.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-9", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "float", "float"], "patching_rect": [610.0, 197.0, 85.0, 22.0], "text": "unpack s 0. 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-79", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [523.0, 230.0, 105.0, 22.0], "text": "scale 0. 5. 0. 400."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-78", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [31.0, 455.0, 640.0, 480.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-5", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [205.0, 70.0, 37.0, 22.0], "text": "reset"}}, {"box": {"comment": "", "id": "obj-24", "index": 1, "maxclass": "outlet", "numinlets": 1, "numoutlets": 0, "patching_rect": [161.0, 387.0, 25.0, 25.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-23", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [255.0, 167.0, 32.5, 22.0], "text": "0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-21", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [97.0, 65.0, 46.0, 22.0], "text": "launch"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-18", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [131.0, 352.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [161.0, 309.0, 51.0, 22.0], "text": "+ 0.001"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [161.0, 263.0, 47.0, 22.0], "text": "float 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-8", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [207.0, 164.0, 32.5, 22.0], "text": "0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-6", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [161.0, 164.0, 32.5, 22.0], "text": "1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [161.0, 204.0, 51.0, 22.0], "text": "metro 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [161.0, 112.0, 111.0, 22.0], "text": "select launch reset"}}, {"box": {"comment": "", "id": "obj-1", "index": 1, "maxclass": "inlet", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [161.0, 54.0, 25.0, 25.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-16", 0], "order": 0, "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-18", 0], "order": 1, "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-15", 1], "midpoints": [170.5, 337.0, 214.0, 337.0, 214.0, 251.0, 198.5, 251.0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-23", 0], "order": 0, "source": ["obj-2", 1]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-8", 0], "order": 1, "source": ["obj-2", 1]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-15", 1], "midpoints": [264.5, 240.0, 198.5, 240.0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-15", 0], "midpoints": [216.5, 231.0, 170.5, 231.0], "order": 0, "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-3", 0], "order": 1, "source": ["obj-8", 0]}}], "originid": "pat-2259"}, "patching_rect": [523.0, 146.0, 48.0, 22.0], "saved_object_attributes": {"globalpatchername": ""}, "text": "p clock"}}, {"box": {"id": "obj-74", "maxclass": "lcd", "numinlets": 1, "numoutlets": 4, "outlettype": ["list", "list", "int", ""], "patching_rect": [443.0, 396.0, 470.0, 276.0], "presentation": 1, "presentation_rect": [423.0, 377.0, 470.0, 276.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-73", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [385.0, 278.0, 32.5, 22.0], "text": "!- 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-72", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [385.0, 232.0, 43.0, 22.0], "text": "abs 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-69", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [371.0, 367.0, 32.5, 22.0], "text": "/ 2."}}, {"box": {"floatoutput": 1, "id": "obj-68", "maxclass": "slider", "mult": 0.0001, "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [312.0, 429.0, 27.0, 158.0], "size": 5.0}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-67", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [312.0, 398.0, 32.5, 22.0], "text": "+ 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-65", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [347.0, 311.0, 43.0, 22.0], "text": "pow 2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-64", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [347.0, 280.0, 21.0, 22.0], "text": "r t"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-63", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [371.0, 341.0, 32.5, 22.0], "text": "* 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-62", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [312.0, 306.0, 21.0, 22.0], "text": "r t"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-61", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [207.0, 544.0, 23.0, 22.0], "text": "s t"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-60", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [244.0, 510.0, 18.0, 20.0], "text": "t"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-57", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [273.0, 145.0, 29.0, 20.0], "text": "v0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-55", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [312.0, 341.0, 32.5, 22.0], "text": "* 0."}}, {"box": {"floatoutput": 1, "id": "obj-54", "maxclass": "slider", "mult": 0.0001, "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [193.0, 595.0, 214.0, 26.0], "size": 5.0}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-53", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [260.0, 248.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-51", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [193.0, 235.0, 32.5, 22.0], "text": "t f f"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-50", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [127.0, 203.0, 33.0, 22.0], "text": "float"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-48", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [127.0, 262.0, 32.5, 22.0], "text": "* 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-46", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [193.0, 509.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [231.0, 359.0, 49.0, 22.0], "text": "* 1000."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-42", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [193.0, 387.0, 63.0, 22.0], "text": "pack 0. 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-41", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [193.0, 428.0, 54.0, 22.0], "text": "0, $1 $2"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-36", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [237.0, 300.0, 61.0, 20.0], "text": "t=2(v0/a)"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [193.0, 329.0, 43.0, 22.0], "text": "abs 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-30", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [193.0, 302.0, 32.5, 22.0], "text": "* 2."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [193.0, 271.0, 32.5, 22.0], "text": "/ 0."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-27", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 5, "outlettype": ["bang", "bang", "bang", "bang", "bang"], "patching_rect": [176.0, 101.0, 74.0, 22.0], "text": "bangbang 5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-20", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [176.0, 74.0, 46.0, 22.0], "text": "launch"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [193.0, 204.0, 33.0, 22.0], "text": "float"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [193.0, 472.0, 53.0, 22.0], "text": "line 0. 1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-12", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [363.0, 143.0, 26.0, 20.0], "text": "a"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-10", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [458.0, 142.0, 22.0, 20.0], "text": "Cr"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-8", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [313.0, 249.0, 29.0, 20.0], "text": "Vi"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-6", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [311.0, 143.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-4", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [403.0, 143.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "format": 6, "id": "obj-2", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [222.0, 144.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-80", "linecount": 3, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [443.0, 214.0, 69.0, 50.0], "text": "getsize, brgb 0 0 0, clear"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 12.0, "id": "obj-5", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [451.0, 360.0, 37.0, 22.0], "text": "clear"}}], "lines": [{"patchline": {"destination": ["obj-95", 2], "order": 0, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-95", 0], "order": 1, "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-13", 0], "midpoints": [150.5, 458.0, 202.5, 458.0], "source": ["obj-101", 0]}}, {"patchline": {"destination": ["obj-101", 0], "source": ["obj-103", 0]}}, {"patchline": {"destination": ["obj-7", 0], "source": ["obj-105", 0]}}, {"patchline": {"destination": ["obj-108", 0], "source": ["obj-106", 0]}}, {"patchline": {"destination": ["obj-20", 0], "midpoints": [87.5, 89.0, 172.0, 89.0, 172.0, 69.0, 185.5, 69.0], "source": ["obj-108", 0]}}, {"patchline": {"destination": ["obj-37", 0], "midpoints": [111.0, 84.0, 155.0, 84.0, 155.0, 67.0, 483.5, 67.0], "source": ["obj-108", 1]}}, {"patchline": {"destination": ["obj-97", 0], "source": ["obj-109", 0]}}, {"patchline": {"destination": ["obj-93", 2], "source": ["obj-113", 0]}}, {"patchline": {"destination": ["obj-93", 0], "source": ["obj-114", 0]}}, {"patchline": {"destination": ["obj-93", 1], "source": ["obj-115", 0]}}, {"patchline": {"destination": ["obj-93", 3], "source": ["obj-116", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-117", 0]}}, {"patchline": {"destination": ["obj-38", 0], "order": 0, "source": ["obj-123", 2]}}, {"patchline": {"destination": ["obj-58", 1], "midpoints": [947.0, 288.0, 850.0, 328.0], "order": 1, "source": ["obj-123", 1]}}, {"patchline": {"destination": ["obj-70", 0], "order": 0, "source": ["obj-123", 1]}}, {"patchline": {"destination": ["obj-95", 3], "midpoints": [859.0, 230.0, 878.0, 273.0, 902.0, 282.0, 877.0, 308.0], "order": 1, "source": ["obj-123", 2]}}, {"patchline": {"destination": ["obj-105", 0], "midpoints": [236.5, 503.0, 86.5, 503.0], "order": 1, "source": ["obj-13", 1]}}, {"patchline": {"destination": ["obj-46", 0], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-50", 0], "midpoints": [236.5, 500.0, 120.0, 500.0, 120.0, 187.0, 136.5, 187.0], "order": 0, "source": ["obj-13", 1]}}, {"patchline": {"destination": ["obj-16", 0], "order": 4, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-35", 0], "order": 3, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-45", 0], "order": 2, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-71", 0], "order": 1, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-84", 0], "order": 0, "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-128", 0], "source": ["obj-141", 0]}}, {"patchline": {"destination": ["obj-2", 0], "midpoints": [252.5, 120.0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-51", 0], "order": 1, "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-53", 0], "midpoints": [202.5, 224.0, 269.5, 224.0], "order": 0, "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-17", 1], "midpoints": [231.5, 172.0, 216.5, 172.0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-105", 0], "midpoints": [185.5, 97.0, 86.5, 97.0], "order": 2, "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-27", 0], "order": 1, "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-78", 0], "midpoints": [185.5, 92.0, 162.0, 92.0, 162.0, 176.0, 510.0, 176.0, 510.0, 120.0, 532.5, 120.0], "order": 0, "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-1", 2], "midpoints": [565.5, 118.0, 736.0, 118.0, 736.0, 211.0, 798.0, 211.0, 798.0, 240.0, 815.0, 240.0, 815.0, 277.0], "order": 0, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-104", 0], "order": 2, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-109", 0], "midpoints": [565.5, 124.0, 720.0, 124.0, 720.0, 159.0, 737.0, 159.0, 737.0, 185.0, 782.5, 185.0], "order": 1, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-79", 2], "midpoints": [565.5, 144.0, 583.0, 144.0, 583.0, 220.0, 566.9, 220.0], "order": 3, "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-77", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-17", 0], "midpoints": [185.5, 141.0, 202.5, 141.0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-2", 0], "midpoints": [199.25, 137.0, 231.5, 137.0], "source": ["obj-27", 1]}}, {"patchline": {"destination": ["obj-4", 0], "midpoints": [226.75, 130.0, 412.5, 130.0], "source": ["obj-27", 3]}}, {"patchline": {"destination": ["obj-6", 0], "midpoints": [213.0, 134.0, 320.5, 134.0], "source": ["obj-27", 2]}}, {"patchline": {"destination": ["obj-80", 0], "midpoints": [240.5, 126.0, 488.0, 126.0, 488.0, 181.0, 452.5, 181.0], "source": ["obj-27", 4]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-58", 3], "order": 0, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-58", 0], "order": 1, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-42", 0], "order": 1, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-43", 0], "midpoints": [202.5, 353.0, 240.5, 353.0], "order": 0, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-123", 0], "midpoints": [619.5, 195.0, 687.0, 195.0, 687.0, 174.0, 846.5, 174.0], "order": 0, "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-9", 0], "order": 1, "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-6", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-102", 0], "order": 1, "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-78", 0], "midpoints": [483.5, 97.0, 532.5, 97.0], "order": 0, "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-3", 3], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-50", 1], "midpoints": [412.5, 179.0, 150.5, 179.0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-42", 1], "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-54", 0], "order": 1, "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-61", 0], "midpoints": [202.5, 539.0, 216.5, 539.0], "order": 0, "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-49", 1], "midpoints": [652.5, 280.0, 548.5, 280.0], "source": ["obj-47", 0]}}, {"patchline": {"destination": ["obj-17", 0], "midpoints": [136.5, 288.0, 175.0, 288.0, 175.0, 193.0, 202.5, 193.0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-56", 0], "midpoints": [532.5, 305.0, 561.0, 305.0, 561.0, 281.0, 573.5, 281.0], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-48", 1], "midpoints": [216.0, 260.0, 165.0, 260.0, 165.0, 260.0, 150.0, 260.0], "source": ["obj-51", 1]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-55", 1], "midpoints": [269.5, 276.0, 335.0, 276.0], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-67", 0], "source": ["obj-55", 0]}}, {"patchline": {"destination": ["obj-113", 0], "order": 0, "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-114", 0], "order": 1, "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-52", 0], "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-29", 1], "midpoints": [320.5, 233.0, 237.0, 233.0, 237.0, 266.0, 216.0, 266.0], "order": 1, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-72", 0], "midpoints": [320.5, 218.0, 394.5, 218.0], "order": 0, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-62", 0]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-63", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-64", 0]}}, {"patchline": {"destination": ["obj-63", 0], "source": ["obj-65", 0]}}, {"patchline": {"destination": ["obj-66", 0], "midpoints": [321.5, 425.0, 360.5, 425.0], "order": 0, "source": ["obj-67", 0]}}, {"patchline": {"destination": ["obj-68", 0], "order": 1, "source": ["obj-67", 0]}}, {"patchline": {"destination": ["obj-67", 1], "midpoints": [380.5, 392.0, 335.0, 392.0], "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-1", 4], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-63", 1], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-23", 0], "midpoints": [903.5, 674.0, 934.0, 674.0, 934.0, 136.0, 619.5, 136.0], "order": 1, "source": ["obj-74", 3]}}, {"patchline": {"destination": ["obj-32", 0], "midpoints": [903.5, 674.0, 934.0, 674.0, 934.0, 167.0, 659.0, 167.0, 659.0, 167.0, 619.5, 167.0], "order": 0, "source": ["obj-74", 3]}}, {"patchline": {"destination": ["obj-76", 0], "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-83", 0], "source": ["obj-76", 0]}}, {"patchline": {"destination": ["obj-104", 0], "order": 2, "source": ["obj-77", 0]}}, {"patchline": {"destination": ["obj-3", 2], "midpoints": [749.5, 114.0, 955.0, 114.0, 955.0, 303.0, 907.5, 303.0], "order": 1, "source": ["obj-77", 0]}}, {"patchline": {"destination": ["obj-76", 2], "order": 3, "source": ["obj-77", 0]}}, {"patchline": {"destination": ["obj-89", 0], "midpoints": [749.5, 171.0, 926.5, 171.0], "order": 0, "source": ["obj-77", 0]}}, {"patchline": {"destination": ["obj-141", 0], "order": 0, "source": ["obj-78", 0]}}, {"patchline": {"destination": ["obj-79", 0], "order": 1, "source": ["obj-78", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-79", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-80", 0]}}, {"patchline": {"destination": ["obj-3", 0], "midpoints": [907.5, 309.0, 875.5, 309.0], "source": ["obj-82", 0]}}, {"patchline": {"destination": ["obj-115", 0], "order": 1, "source": ["obj-83", 0]}}, {"patchline": {"destination": ["obj-116", 0], "order": 0, "source": ["obj-83", 0]}}, {"patchline": {"destination": ["obj-77", 0], "source": ["obj-84", 0]}}, {"patchline": {"destination": ["obj-82", 0], "source": ["obj-86", 0]}}, {"patchline": {"destination": ["obj-86", 0], "source": ["obj-89", 0]}}, {"patchline": {"destination": ["obj-47", 0], "order": 0, "source": ["obj-9", 1]}}, {"patchline": {"destination": ["obj-79", 4], "midpoints": [652.5, 221.0, 601.3, 221.0], "order": 1, "source": ["obj-9", 1]}}, {"patchline": {"destination": ["obj-85", 0], "midpoints": [685.5, 218.0, 678.0, 218.0, 678.0, 251.0, 665.5, 251.0], "order": 1, "source": ["obj-9", 2]}}, {"patchline": {"destination": ["obj-98", 0], "midpoints": [685.5, 222.0, 768.5, 222.0], "order": 0, "source": ["obj-9", 2]}}, {"patchline": {"destination": ["obj-82", 0], "order": 0, "source": ["obj-92", 0]}}, {"patchline": {"destination": ["obj-96", 0], "order": 1, "source": ["obj-92", 0]}}, {"patchline": {"destination": ["obj-117", 0], "source": ["obj-93", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-94", 0]}}, {"patchline": {"destination": ["obj-94", 0], "source": ["obj-95", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-96", 0]}}, {"patchline": {"destination": ["obj-96", 0], "source": ["obj-97", 0]}}, {"patchline": {"destination": ["obj-76", 3], "source": ["obj-98", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-99", 0]}}], "originid": "pat-2255", "dependency_cache": [], "autosave": 0}}