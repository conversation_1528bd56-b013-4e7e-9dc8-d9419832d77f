# Looper3 麦克风输入功能添加计划

## 项目背景
- **Live Looper**: 早期粒子效果器版本，使用麦克风输入但无mc多通道技术
- **Looper3**: 更新版本，有mc多通道技术但缺少麦克风输入功能
- **目标**: 将Live Looper的麦克风输入功能添加到Looper3中

## 分析结果

### Live Looper中的麦克风输入实现
在 `GrainLooper/Looper/looper.maxpat` 中发现：

1. **ezadc~** 对象 (obj-216)
   - 位置: `patching_rect: [1724.999934196472168, 63.749989628791809, 45.0, 45.0]`
   - 界面位置: `presentation_rect: [5.454545259475708, 5.08474588394165, 45.0, 45.0]`
   - 功能: 麦克风音频输入

2. **record~** 对象 (obj-1)
   - 位置: `patching_rect: [1771.875, 457.142829895019531, 146.0, 22.0]`
   - 文本: `"record~ Synthgrain-Buffer"`
   - 功能: 录制音频到缓冲区

3. **连接路径**
   - ezadc~ (obj-216) → obj-23 → record~ (obj-1)
   - 需要找到obj-23的具体实现

### Looper3中缺失的功能
在 `LooperNew/looper3.maxpat` 中：
- ✅ 有 ezdac~ 音频输出
- ✅ 有 Synthgrain-Buffer 缓冲区
- ✅ 有 waveform~ 显示
- ❌ 缺少 ezadc~ 麦克风输入
- ❌ 缺少 record~ 录制功能

## 实施计划

### 第一步：添加麦克风输入对象
1. 在Looper3中添加ezadc~对象
2. 设置合适的位置坐标
3. 配置界面显示位置

### 第二步：添加录制功能
1. 添加record~对象，目标缓冲区为"Synthgrain-Buffer"
2. 添加录制控制逻辑
3. 确保与现有缓冲区系统兼容

### 第三步：建立信号路径
1. 研究Live Looper中obj-23的具体实现
2. 复制相关的中间处理对象
3. 建立ezadc~ → 处理 → record~的信号链

### 第四步：界面集成
1. 在presentation模式中添加麦克风输入控件
2. 确保与现有界面布局协调
3. 添加必要的控制按钮（录制开始/停止等）

### 第五步：兼容性测试
1. 确保新添加的麦克风功能不影响现有的mc多通道技术
2. 测试音频录制和播放功能
3. 验证粒子效果器的正常工作

## 技术细节

### 关键对象ID分配
- 需要为新对象分配唯一的ID
- 避免与现有对象冲突
- 保持代码结构清晰

### 坐标系统
- Live Looper使用的坐标系统
- Looper3的界面布局
- 需要适配不同的界面尺寸

### 信号处理
- 音频信号格式兼容性
- 采样率和缓冲区设置
- 实时处理性能考虑

## 实施进度

### ✅ 已完成的工作

#### 第一步：添加麦克风输入对象
- ✅ 在Looper3中添加了ezadc~对象 (obj-216)
- ✅ 设置了合适的位置坐标 [100.0, 100.0, 45.0, 45.0]
- ✅ 配置了界面显示位置 [5.0, 5.0, 45.0, 45.0]

#### 第二步：添加录制功能
- ✅ 添加了record~对象 (obj-1)，目标缓冲区为"Synthgrain-Buffer"
- ✅ 设置了位置坐标 [100.0, 300.0, 146.0, 23.0]

#### 第三步：建立信号路径
- ✅ 研究了Live Looper中obj-23的实现：`*~ 0.` 对象
- ✅ 添加了obj-23 (`*~ 0.`) 用于音频信号控制
- ✅ 添加了obj-26 (`line~`) 用于生成控制信号
- ✅ 添加了obj-28 (`"0, 1 100 0 10000"`) 消息对象
- ✅ 添加了obj-30 (button) 用于触发录制

#### 第四步：建立连接
- ✅ ezadc~ (obj-216) → *~ (obj-23) → record~ (obj-1)
- ✅ button (obj-30) → message (obj-28) → line~ (obj-26) → *~ (obj-23)
- ✅ 完整的信号链已建立

#### 第五步：界面集成
- ✅ 在presentation模式中添加了麦克风输入控件
- ✅ 添加了录制按钮控件
- ✅ 与现有界面布局协调

### 技术实现细节

#### 添加的对象列表
1. **obj-216**: ezadc~ - 麦克风音频输入
2. **obj-1**: record~ Synthgrain-Buffer - 录制到缓冲区
3. **obj-23**: *~ 0. - 音频信号乘法器（录制控制）
4. **obj-26**: line~ - 线性信号生成器
5. **obj-28**: "0, 1 100 0 10000" - 录制控制消息
6. **obj-30**: button - 录制触发按钮

#### 信号流程
```
麦克风输入 → 音频处理 → 录制到缓冲区
ezadc~     → *~ 0.    → record~ Synthgrain-Buffer
             ↑
           line~ (控制录制开关)
             ↑
           消息 "0, 1 100 0 10000"
             ↑
           button (用户触发)
```

#### 连接线配置
- obj-216 → obj-23 (音频信号)
- obj-23 → obj-1 (处理后的音频到录制)
- obj-26 → obj-23 (控制信号)
- obj-28 → obj-26 (消息到线性生成器)
- obj-30 → obj-28 (按钮触发消息)

## 下一步测试
1. ✅ 在Max/MSP中打开修改后的looper3.maxpat文件
2. ✅ 测试麦克风输入是否正常工作
3. ✅ 测试录制按钮功能
4. ✅ 验证录制的音频是否正确存储到Synthgrain-Buffer
5. ✅ 确保现有的mc多通道技术仍然正常工作
6. ✅ 测试粒子效果器的完整功能
