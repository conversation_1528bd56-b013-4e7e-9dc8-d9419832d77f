{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 77.0, 1580.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-154", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [474.026309967041, 690.7894670963287, 24.0, 24.0]}}, {"box": {"id": "obj-152", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [498.0263110399246, 573.0263103246689, 24.0, 24.0]}}, {"box": {"id": "obj-92", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [466.4473639726639, 980.9210432767868, 31.578945994377136, 20.0], "text": "max"}}, {"box": {"id": "obj-90", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [367.76315438747406, 797.3684134483337, 44.44444251060486, 20.0], "presentation": 1, "presentation_rect": [412.31724828481674, 773.39472925663, 44.44444251060486, 20.0], "text": "on/off"}}, {"box": {"id": "obj-88", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [557.2368367910385, 613.8157836198807, 50.0, 20.0], "presentation": 1, "presentation_rect": [456.5789430141449, 773.39472925663, 50.0, 20.0], "text": "bpm"}}, {"box": {"id": "obj-79", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [414.47368025779724, 1006.5789377689362, 24.0, 24.0], "presentation": 1, "presentation_rect": [414.47368025779724, 910.5263071060181, 40.13157856464386, 40.13157856464386]}}, {"box": {"id": "obj-73", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [440.1315747499466, 796.7105187177658, 50.0, 22.0]}}, {"box": {"id": "obj-71", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [440.1315747499466, 747.3684139251709, 107.23684108257294, 22.0], "text": "/"}}, {"box": {"id": "obj-70", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [538.8157843351364, 635.8157836198807, 50.0, 22.0], "presentation": 1, "presentation_rect": [452.6315746307373, 795.39472925663, 48.026315331459045, 22.0]}}, {"box": {"id": "obj-68", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [440.1315747499466, 612.8157836198807, 42.0, 22.0], "text": "60000"}}, {"box": {"id": "obj-65", "maxclass": "led", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [470.3947323560715, 954.6052540540695, 24.0, 24.0], "presentation": 1, "presentation_rect": [456.5789430141449, 910.5263071060181, 40.13157856464386, 40.13157856464386]}}, {"box": {"id": "obj-40", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [414.47368025779724, 955.9210435152054, 50.0, 22.0]}}, {"box": {"id": "obj-38", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [414.47368025779724, 795.39472925663, 24.0, 24.0], "presentation": 1, "presentation_rect": [414.47368025779724, 795.39472925663, 40.13157856464386, 40.13157856464386], "svg": ""}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [414.47368025779724, 848.0263077020645, 45.0, 22.0], "text": "metro i"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [414.47368025779724, 894.7368335723877, 69.0, 22.0], "text": "counter 1 8"}}, {"box": {"id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [190.78947186470032, 979.9210432767868, 40.0, 22.0], "text": "select"}}], "lines": [{"patchline": {"destination": ["obj-68", 0], "order": 1, "source": ["obj-152", 0]}}, {"patchline": {"destination": ["obj-70", 0], "order": 0, "source": ["obj-152", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-154", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-32", 2]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-79", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-154", 0], "order": 1, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-71", 1], "order": 0, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-36", 1], "source": ["obj-73", 0]}}], "originid": "pat-11", "dependency_cache": [], "autosave": 0, "toolbaradditions": ["BEAP", "packagemanager"]}}