{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [2097.0, 318.0, 995.0, 778.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [587.0, 492.0, 100.0, 22.0]}}, {"box": {"id": "obj-42", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [476.0, 187.0, 50.0, 22.0], "text": "0"}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [495.0, 134.0, 129.0, 22.0], "text": "random @range 1. 10."}}, {"box": {"id": "obj-39", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [489.0, 86.0, 24.0, 24.0]}}, {"box": {"id": "obj-30", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 8, "outlettype": ["", "", "", "int", "int", "", "int", ""], "patching_rect": [703.0, 543.0, 92.5, 22.0], "text": "mid<PERSON><PERSON>e"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-26", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [654.0, 916.0, 140.770111, 23.0], "text": "midievent 160 60 5"}}, {"box": {"id": "obj-52", "linecount": 5, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [654.0, 951.0, 195.0, 75.0], "style": "helpfile_label", "text": "The rightmost outlet of the midiformat object converts MIDI input into properly formatted midievent messages for use with the vst~ object."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-24", "linecount": 3, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [372.0, 977.0, 251.0, 51.0], "style": "helpfile_label", "text": "First number in series is the status byte, which combines inlet (type of message) with channel to make one number."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-22", "linecount": 4, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [459.0, 905.0, 148.0, 66.0], "style": "helpfile_label", "text": "Combine bytes into list to show how a series of separate messages work together in MIDI."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-20", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [152.0, 932.0, 146.0, 21.0], "style": "helpfile_label", "text": "One byte is 256 values"}}, {"box": {"border": 0, "filename": "helpargs.js", "id": "obj-21", "ignoreclick": 1, "jsarguments": ["midiformat"], "maxclass": "jsui", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [508.0, 856.0, 290.12799072265625, 39.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-17", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [264.0, 719.0, 32.5, 23.0], "text": "1 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-18", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [379.0, 951.0, 76.333344, 23.0], "text": "160 60 5"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-16", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [529.0, 827.0, 76.0, 25.0], "text": "Channel"}}, {"box": {"id": "obj-15", "items": [1, ",", 2, ",", 3, ",", 4, ",", 5, ",", 6, ",", 7, ",", 8, ",", 9, ",", 10, ",", 11, ",", 12, ",", 13, ",", 14, ",", 15, ",", 16], "maxclass": "umenu", "numinlets": 1, "numoutlets": 3, "outlettype": ["int", "", ""], "parameter_enable": 0, "patching_rect": [474.0, 828.0, 50.0, 22.0]}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-13", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [186.0, 977.0, 170.0, 40.0], "text": "Output of midiformat typically goes to midiout."}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [131.0, 986.0, 52.0, 23.0], "text": "midiout"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [379.0, 905.0, 47.0, 23.0], "text": "thresh"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-9", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [152.0, 905.0, 152.0, 23.0], "text": "print midibyte @popup 1"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-8", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [464.0, 801.0, 89.0, 25.0], "text": "Pitch Bend"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-12", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [407.0, 772.0, 138.0, 25.0], "text": "Channel Aftertouch"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-14", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [344.0, 742.0, 127.0, 25.0], "text": "Program Change"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-19", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [297.0, 703.0, 228.0, 25.0], "text": "Control Change <controller, value>"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-23", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [233.0, 657.0, 292.0, 25.0], "text": "Polyphonic (Key) Aftertouch <pitch, pressure>"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-25", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [186.0, 629.0, 76.0, 25.0], "text": "Note Off"}}, {"box": {"bubble": 1, "fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-27", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [181.0, 600.0, 76.0, 25.0], "text": "Note On"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 7, "numoutlets": 2, "outlettype": ["int", ""], "patching_rect": [131.0, 856.0, 377.0, 23.0], "text": "midiformat"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-32", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [192.0, 658.0, 36.0, 23.0], "text": "60 5"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-33", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [429.0, 802.0, 32.5, 23.0], "text": "64"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-34", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [250.0, 689.0, 43.0, 23.0], "text": "1 127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-35", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [310.0, 743.0, 29.5, 23.0], "text": "1"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-36", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [370.0, 772.0, 32.5, 23.0], "text": "127"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-37", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [147.0, 630.0, 36.0, 23.0], "text": "60 0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-38", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [131.0, 603.0, 50.0, 23.0], "text": "60 127"}}, {"box": {"id": "obj-7", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["int", "int"], "patching_rect": [101.0, 344.0, 55.0, 22.0], "text": "stripnote"}}, {"box": {"id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 7, "numoutlets": 2, "outlettype": ["int", ""], "patching_rect": [254.0, 162.0, 82.0, 22.0], "text": "midiformat"}}, {"box": {"id": "obj-5", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [476.0, 518.0, 47.0, 22.0], "text": "midiout"}}, {"box": {"id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 0, "patching_rect": [322.0, 512.0, 49.0, 22.0], "text": "noteout"}}, {"box": {"id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [710.0, 399.0, 100.0, 22.0]}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [342.0, 407.0, 115.0, 22.0], "text": "makenote 120 2000"}}, {"box": {"id": "obj-1", "maxclass": "kslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["int", "int"], "parameter_enable": 0, "patching_rect": [342.0, 241.0, 336.0, 53.0]}}, {"box": {"background": 1, "bgcolor": [0.9, 0.65, 0.05, 1.0], "fontface": 1, "hint": "", "id": "obj-29", "ignoreclick": 1, "legacytextcolor": 1, "maxclass": "textbutton", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", "int"], "parameter_enable": 0, "patching_rect": [528.0, 706.0, 20.0, 20.0], "rounded": 60.0, "saved_attribute_attributes": {"bgcolor": {"expression": "themecolor.lesson_step_circle"}}, "text": "2", "textcolor": [0.34902, 0.34902, 0.34902, 1.0]}}, {"box": {"background": 1, "bgcolor": [0.9, 0.65, 0.05, 1.0], "fontface": 1, "hint": "", "id": "obj-93", "ignoreclick": 1, "legacytextcolor": 1, "maxclass": "textbutton", "numinlets": 1, "numoutlets": 3, "outlettype": ["", "", "int"], "parameter_enable": 0, "patching_rect": [264.0, 614.0, 20.0, 20.0], "rounded": 60.0, "saved_attribute_attributes": {"bgcolor": {"expression": "themecolor.lesson_step_circle"}}, "text": "1", "textcolor": [0.34902, 0.34902, 0.34902, 1.0]}}], "lines": [{"patchline": {"destination": ["obj-2", 1], "source": ["obj-1", 1]}}, {"patchline": {"destination": ["obj-2", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-18", 1], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-28", 6], "source": ["obj-15", 1]}}, {"patchline": {"destination": ["obj-28", 2], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-4", 1], "source": ["obj-2", 1]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-10", 0], "order": 0, "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-11", 0], "order": 2, "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-26", 1], "midpoints": [498.5, 905.5363159999999, 785.270111, 905.5363159999999], "source": ["obj-28", 1]}}, {"patchline": {"destination": ["obj-9", 0], "order": 1, "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-28", 1], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-28", 5], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-28", 2], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-28", 3], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-28", 4], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-42", 1], "source": ["obj-40", 0]}}], "originid": "pat-11", "dependency_cache": [{"name": "helpargs.js", "bootpath": "C74:/help/resources", "type": "TEXT", "implicit": 1}], "autosave": 0}}