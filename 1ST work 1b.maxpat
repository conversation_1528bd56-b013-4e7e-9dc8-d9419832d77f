{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 77.0, 1580.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-117", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.ADSR.maxpat", "numinlets": 2, "numoutlets": 1, "offset": [0.0, 0.0], "outlettype": ["signal"], "patching_rect": [233.85962980985641, 577.6315734386444, 234.0, 116.0], "varname": "bp.ADSR", "viewvisibility": 1}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-116", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.VCA.maxpat", "numinlets": 2, "numoutlets": 1, "offset": [0.0, 0.0], "outlettype": ["signal"], "patching_rect": [43.85964721441269, 767.7631505727768, 113.0, 116.0], "varname": "bp.VCA", "viewvisibility": 1}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-115", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.Keyboard.maxpat", "numinlets": 0, "numoutlets": 4, "offset": [0.0, 0.0], "outlettype": ["signal", "signal", "signal", "signal"], "patching_rect": [11.184210419654846, 415.1315749883652, 578.0, 117.0], "varname": "bp.Keyboard", "viewvisibility": 1}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-113", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.FM.maxpat", "numinlets": 2, "numoutlets": 1, "offset": [0.0, 0.0], "outlettype": ["signal"], "patching_rect": [11.184210419654846, 577.6315734386444, 211.0, 116.0], "varname": "bp.FM", "viewvisibility": 1}}, {"box": {"channels": 1, "fontsize": 13.0, "id": "obj-110", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 1, "numoutlets": 4, "orientation": 1, "outlettype": ["signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [43.85964721441269, 1002.6315693855286, 136.0, 35.0], "saved_attribute_attributes": {"valueof": {"parameter_initial": [-70], "parameter_initial_enable": 1, "parameter_longname": "live.gain~[1]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "showname": 0, "varname": "live.gain~[1]"}}, {"box": {"id": "obj-92", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [496.2105052471161, 1074.1227613687515, 31.578945994377136, 20.0], "text": "max"}}, {"box": {"id": "obj-90", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [397.41518729925156, 890.8888502120972, 44.44444251060486, 20.0], "text": "on/off"}}, {"box": {"id": "obj-88", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [586.5496820807457, 707.239734351635, 50.0, 20.0], "text": "bpm"}}, {"box": {"id": "obj-86", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [568.7295078635216, 792.9824216365814, 29.5, 22.0], "text": "- 0"}}, {"box": {"id": "obj-81", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [530.4093336462975, 760.8186803460121, 24.0, 24.0]}}, {"box": {"id": "obj-79", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [443.8596298098564, 1099.9999521374702, 24.0, 24.0]}}, {"box": {"id": "obj-75", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [469.8596298098564, 728.0701437592506, 24.0, 24.0]}}, {"box": {"id": "obj-73", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [469.8596298098564, 889.8888502120972, 50.0, 22.0]}}, {"box": {"id": "obj-71", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [469.8596298098564, 840.3508406281471, 29.5, 22.0], "text": "/"}}, {"box": {"id": "obj-70", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [586.5496820807457, 729.239734351635, 50.0, 22.0]}}, {"box": {"id": "obj-68", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [469.8596298098564, 785.3800827860832, 42.0, 22.0], "text": "60000"}}, {"box": {"id": "obj-65", "maxclass": "led", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [499.99997824430466, 1048.1227613687515, 24.0, 24.0]}}, {"box": {"id": "obj-44", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [43.85964721441269, 1068.421006143093, 45.0, 45.0]}}, {"box": {"id": "obj-40", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [443.8596298098564, 1049.1227613687515, 50.0, 22.0]}}, {"box": {"id": "obj-38", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [443.8596298098564, 888.8888502120972, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [443.8596298098564, 941.5204268693924, 45.0, 22.0], "text": "metro i"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [443.8596298098564, 988.3040505647659, 69.0, 22.0], "text": "counter 1 8"}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [258.3947256207466, 1057.894690811634, 39.0, 22.0], "text": "metro"}}, {"box": {"id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [257.8947256207466, 988.3040505647659, 40.0, 22.0], "text": "select"}}], "lines": [{"patchline": {"destination": ["obj-44", 1], "order": 0, "source": ["obj-110", 0]}}, {"patchline": {"destination": ["obj-44", 0], "order": 1, "source": ["obj-110", 0]}}, {"patchline": {"destination": ["obj-116", 1], "source": ["obj-113", 0]}}, {"patchline": {"destination": ["obj-110", 0], "source": ["obj-116", 0]}}, {"patchline": {"destination": ["obj-116", 0], "source": ["obj-117", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-32", 2]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-79", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-81", 0], "order": 1, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-86", 0], "order": 0, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-36", 1], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-68", 0], "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-81", 0]}}, {"patchline": {"destination": ["obj-71", 1], "source": ["obj-86", 0]}}], "originid": "pat-11", "parameters": {"obj-110": ["live.gain~[1]", "live.gain~", 0], "obj-113::obj-20": ["mute", "mute", 0], "obj-113::obj-56": ["De<PERSON><PERSON>", "De<PERSON><PERSON>", 0], "obj-113::obj-80": ["<PERSON><PERSON>", "<PERSON><PERSON>", 0], "obj-113::obj-86": ["Amt", "Amt", 0], "obj-113::obj-91": ["Offset", "Offset", 0], "obj-115::obj-12": ["KeyboardMode", "KeyboardMode", 0], "obj-115::obj-15::obj-2": ["pastebang", "pastebang", 0], "obj-115::obj-48": ["live.text", "live.text", 0], "obj-115::obj-5": ["Octave", "Octave", 0], "obj-115::obj-52": ["octave", "octave", 0], "obj-115::obj-68": ["RepeatInterval", "RepeatInterval", 0], "obj-115::obj-71": ["velocity", "velocity", 0], "obj-116::obj-33": ["Quadrants", "Quadrants", 0], "obj-116::obj-55": ["Bypass", "Bypass", 0], "obj-116::obj-80": ["Response", "Response", 0], "obj-117::obj-1": ["Attack[1]", "Attack", 0], "obj-117::obj-15": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 0], "obj-117::obj-20": ["Mute", "Mute", 0], "obj-117::obj-29": ["Decay[1]", "Decay", 0], "obj-117::obj-31": ["Release[1]", "Release", 0], "obj-117::obj-32": ["Su<PERSON>in[1]", "<PERSON><PERSON><PERSON>", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "parameter_overrides": {"obj-117::obj-1": {"parameter_longname": "Attack[1]"}, "obj-117::obj-29": {"parameter_longname": "Decay[1]"}, "obj-117::obj-31": {"parameter_longname": "Release[1]"}, "obj-117::obj-32": {"parameter_longname": "Su<PERSON>in[1]"}}, "inherited_shortname": 1}, "dependency_cache": [{"name": "background_sm.maxpat", "bootpath": "C74:/packages/Beap/misc", "type": "JSON", "implicit": 1}, {"name": "bp.ADSR.maxpat", "bootpath": "C74:/packages/Beap/clippings/BEAP/Envelope", "type": "JSON", "implicit": 1}, {"name": "bp.FM.maxpat", "bootpath": "C74:/packages/Beap/clippings/BEAP/Oscillator", "type": "JSON", "implicit": 1}, {"name": "bp.Keyboard.maxpat", "bootpath": "C74:/packages/Beap/clippings/BEAP/Input", "type": "JSON", "implicit": 1}, {"name": "bp.VCA.maxpat", "bootpath": "C74:/packages/Beap/clippings/BEAP/Level", "type": "JSON", "implicit": 1}, {"name": "pastebang.maxpat", "bootpath": "C74:/packages/Beap/misc", "type": "JSON", "implicit": 1}], "autosave": 0, "toolbaradditions": ["BEAP", "packagemanager"]}}