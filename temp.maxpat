{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [155.0, 134.0, 1213.0, 811.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [316.8674815893173, 931.0, 83.0, 22.0], "text": "loadmess 100"}}, {"box": {"id": "obj-94", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [203.12049043178558, 10.843373894691467, 90.0, 22.0], "text": "loadmess 8000"}}, {"box": {"id": "obj-93", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [359.95182156562805, 600.0000221729279, 77.0, 22.0], "text": "loadmess 85"}}, {"box": {"id": "obj-90", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [574.6988164186478, 1383.1325812339783, 35.0, 22.0], "text": "clear"}}, {"box": {"id": "obj-86", "maxclass": "live.scope~", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [492.77110254764557, 1806.0241631269455, 184.0, 68.0]}}, {"box": {"id": "obj-85", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [345.78314530849457, 1400.0000517368317, 29.5, 22.0], "text": "1"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-69", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["signal", "signal", "", ""], "patching_rect": [492.77110254764557, 1726.506087899208, 223.0, 22.0], "text": "adsr~"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-70", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [713.2530384063721, 1672.2892184257507, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-71", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [662.6506268978119, 1672.2892184257507, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-72", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [610.8433960676193, 1672.2892184257507, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "format": 6, "id": "obj-73", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [559.0361652374268, 1672.2892184257507, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-76", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [737.3494248390198, 1627.7109035253525, 50.0, 22.0], "text": "zl nth 8"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-77", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [672.289181470871, 1627.7109035253525, 50.0, 22.0], "text": "zl nth 7"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-78", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [607.2289381027222, 1627.7109035253525, 62.0, 22.0], "text": "zl nth 6"}}, {"box": {"fontname": "<PERSON><PERSON><PERSON>", "fontsize": 11.0, "id": "obj-79", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [542.1686947345734, 1627.7109035253525, 62.0, 22.0], "text": "zl nth 4"}}, {"box": {"id": "obj-81", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [542.1686947345734, 1381.9277619123459, 24.0, 24.0]}}, {"box": {"addpoints": [0.0, 0.0, 0, 16.877983674089958, 1.0, 0, 612.5714176335233, 0.417991937001546, 0, 1621.9408473981189, 0.0, 0, 2582.0, 0.0, 0], "classic_curve": 1, "clickadd": 0, "clicksustain": 0, "domain": 2582.0, "id": "obj-83", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "outputmode": 1, "parameter_enable": 0, "patching_rect": [542.1686947345734, 1448.192824602127, 200.0, 100.0]}}, {"box": {"id": "obj-62", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [181.92771756649017, 1628.9157228469849, 45.0, 45.0]}}, {"box": {"format": 6, "id": "obj-172", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [372.28917038440704, 1312.0482412576675, 50.0, 22.0]}}, {"box": {"id": "obj-154", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [371.08435106277466, 1348.192820906639, 81.0, 22.0], "text": "setdomain $1"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [189.15663349628448, 1355.4217368364334, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-38", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [249.39759957790375, 1118.0723304748535, 39.0, 22.0], "text": "/ 127."}}, {"box": {"id": "obj-169", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [386.74700224399567, 1224.0964307785034, 80.0, 22.0], "text": "loadmess 84."}}, {"box": {"id": "obj-168", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [290.36145651340485, 1224.0964307785034, 93.0, 22.0], "text": "loadmess 3096."}}, {"box": {"id": "obj-164", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [215.66265857219696, 1224.0964307785034, 73.0, 22.0], "text": "loadmess 1."}}, {"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [192.77109146118164, 1172.2891999483109, 32.0, 22.0], "text": "mtof"}}, {"box": {"format": 6, "id": "obj-41", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [290.36145651340485, 1266.265107035637, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-49", "maxclass": "flonum", "maximum": 100.0, "minimum": 1.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [345.78314530849457, 1266.265107035637, 52.0, 23.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-18", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [236.1445870399475, 1266.265107035637, 52.0, 23.0]}}, {"box": {"id": "obj-50", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [189.15663349628448, 1308.4337832927704, 110.38960933685303, 22.0], "text": "reson~"}}, {"box": {"id": "obj-51", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [334.9397714138031, 1346.9880015850067, 24.0, 24.0]}}, {"box": {"id": "obj-53", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [189.15663349628448, 1409.6386063098907, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-54", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [180.7228982448578, 1455.4217405319214, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[1]"}}, {"box": {"id": "obj-56", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [189.15663349628448, 1266.265107035637, 43.0, 22.0], "text": "cycle~"}}, {"box": {"id": "obj-202", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [291.56627583503723, 413.25302731990814, 123.0, 22.0], "text": "scale 1. 0. 150. 4000."}}, {"box": {"id": "obj-201", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [145.78313791751862, 31.325302362442017, 50.0, 22.0]}}, {"box": {"id": "obj-199", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [145.78313791751862, 78.31325590610504, 81.0, 22.0], "text": "setdomain $1"}}, {"box": {"id": "obj-194", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [108.43373894691467, 78.31325590610504, 24.0, 24.0]}}, {"box": {"format": 6, "id": "obj-192", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [191.56627213954926, 378.31326699256897, 50.0, 22.0]}}, {"box": {"id": "obj-190", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [191.56627213954926, 344.5783259868622, 64.0, 22.0], "text": "snapshot~"}}, {"box": {"id": "obj-189", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [45.78313422203064, 304.81928837299347, 106.0, 22.0], "text": "metro 1 @active 1"}}, {"box": {"id": "obj-188", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [191.56627213954926, 304.81928837299347, 58.0, 22.0], "text": "curve~ 0."}}, {"box": {"addpoints_with_curve": [0.0, 0.029901056326412, 0, 0.0, 769.077165679107, 0.714690983055835, 0, -0.15, 2099.303705019321, 0.558371928856499, 0, 0.0, 8000.000000000005, 0.0, 0, -0.4], "classic_curve": 1, "domain": 8000.0, "id": "obj-187", "maxclass": "function", "mode": 1, "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [121.68675148487091, 148.1927765607834, 228.3950799703598, 130.86420798301697]}}, {"box": {"id": "obj-185", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [230.12049043178558, 457.8313422203064, 50.0, 22.0]}}, {"box": {"id": "obj-143", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [316.8674815893173, 990.3614823818207, 50.0, 22.0]}}, {"box": {"id": "obj-123", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [244.5783222913742, 1004.8193142414093, 24.0, 24.0]}}, {"box": {"id": "obj-121", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [244.5783222913742, 1054.216906428337, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-101", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [387.95182156562805, 763.8554499149323, 24.0, 24.0]}}, {"box": {"id": "obj-99", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [387.95182156562805, 796.3855715990067, 59.0, 22.0], "text": "random 3"}}, {"box": {"id": "obj-98", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [387.95182156562805, 837.3494285345078, 50.0, 22.0]}}, {"box": {"id": "obj-96", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [269.8795280456543, 878.3132854700089, 30.0, 22.0], "text": "* 12"}}, {"box": {"id": "obj-92", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [251.80723822116852, 910.8434071540833, 29.5, 22.0], "text": "+"}}, {"box": {"id": "obj-82", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [251.80723822116852, 796.3855715990067, 24.0, 24.0]}}, {"box": {"id": "obj-80", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [251.80723822116852, 837.3494285345078, 129.0, 22.0], "text": "random @range 84 96"}}, {"box": {"id": "obj-75", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [137.34940266609192, 814.4578614234924, 31.111112594604492, 22.0], "text": "int"}}, {"box": {"id": "obj-74", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [192.77109146118164, 939.7590708732605, 50.0, 22.0], "text": "115"}}, {"box": {"format": 6, "id": "obj-57", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [230.12049043178558, 542.1686947345734, 50.0, 22.0]}}, {"box": {"id": "obj-31", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [192.77109146118164, 807.2289454936981, 24.0, 24.0]}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [192.77109146118164, 757.8313533067703, 34.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-58", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [356.62651920318604, 637.3494211435318, 50.0, 22.0]}}, {"box": {"id": "obj-59", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [192.77109146118164, 678.3132780790329, 182.8828827738762, 22.0], "text": "<"}}, {"box": {"id": "obj-16", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [193.97591078281403, 542.1686947345734, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-14", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [245.7831416130066, 571.0843584537506, 150.0, 20.0], "text": "wind velocity"}}, {"box": {"id": "obj-60", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [192.77109146118164, 595.1807448863983, 56.0, 22.0], "text": "metro 50"}}, {"box": {"id": "obj-61", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [192.77109146118164, 632.5301438570023, 73.0, 22.0], "text": "random 100"}}, {"box": {"id": "obj-7", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [212.0, 231.0, 20.0, 20.0]}}, {"box": {"attr": "domain", "id": "obj-87", "maxclass": "attrui", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [663.8554462194443, 1389.1566778421402, 150.0, 22.0]}}], "lines": [{"patchline": {"destination": ["obj-143", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-99", 0], "source": ["obj-101", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-121", 0]}}, {"patchline": {"destination": ["obj-121", 0], "source": ["obj-123", 0]}}, {"patchline": {"destination": ["obj-121", 1], "source": ["obj-143", 0]}}, {"patchline": {"destination": ["obj-60", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-164", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-168", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-169", 0]}}, {"patchline": {"destination": ["obj-154", 0], "source": ["obj-172", 0]}}, {"patchline": {"destination": ["obj-50", 1], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-185", 0]}}, {"patchline": {"destination": ["obj-188", 0], "source": ["obj-187", 1]}}, {"patchline": {"destination": ["obj-190", 0], "source": ["obj-188", 0]}}, {"patchline": {"destination": ["obj-190", 0], "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-192", 0], "source": ["obj-190", 0]}}, {"patchline": {"destination": ["obj-202", 0], "source": ["obj-192", 0]}}, {"patchline": {"destination": ["obj-187", 0], "source": ["obj-194", 0]}}, {"patchline": {"destination": ["obj-187", 0], "source": ["obj-199", 0]}}, {"patchline": {"destination": ["obj-199", 0], "source": ["obj-201", 0]}}, {"patchline": {"destination": ["obj-185", 0], "source": ["obj-202", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-32", 1], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-50", 2], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-51", 0], "midpoints": [202.27109146118164, 1203.8231593370438, 344.4397714138031, 1203.8231593370438], "order": 0, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-56", 0], "order": 1, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-50", 3], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-81", 0], "order": 0, "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-85", 0], "order": 1, "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-54", 1], "order": 0, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-54", 0], "order": 1, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-62", 1], "order": 0, "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-62", 0], "order": 1, "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-60", 1], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-59", 1], "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-101", 0], "order": 0, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-123", 0], "order": 2, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-61", 0], "order": 3, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-82", 0], "order": 1, "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-59", 0], "source": ["obj-61", 0]}}, {"patchline": {"destination": ["obj-53", 1], "order": 1, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-86", 0], "order": 0, "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-69", 4], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-69", 3], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-69", 2], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-69", 1], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-74", 0]}}, {"patchline": {"destination": ["obj-70", 0], "source": ["obj-76", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-77", 0]}}, {"patchline": {"destination": ["obj-72", 0], "source": ["obj-78", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-79", 0]}}, {"patchline": {"destination": ["obj-92", 0], "source": ["obj-80", 0]}}, {"patchline": {"destination": ["obj-83", 0], "source": ["obj-81", 0]}}, {"patchline": {"destination": ["obj-80", 0], "source": ["obj-82", 0]}}, {"patchline": {"destination": ["obj-76", 0], "order": 0, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-77", 0], "order": 1, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-78", 0], "order": 2, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-79", 0], "order": 3, "source": ["obj-83", 1]}}, {"patchline": {"destination": ["obj-81", 0], "midpoints": [732.6686947345734, 1546.9157561063766, 752.4216961860657, 1546.9157561063766, 752.4216961860657, 1371.4096312522888, 551.6686947345734, 1371.4096312522888], "source": ["obj-83", 3]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-85", 0]}}, {"patchline": {"destination": ["obj-83", 0], "source": ["obj-87", 0]}}, {"patchline": {"destination": ["obj-83", 0], "source": ["obj-90", 0]}}, {"patchline": {"destination": ["obj-74", 1], "source": ["obj-92", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-93", 0]}}, {"patchline": {"destination": ["obj-201", 0], "source": ["obj-94", 0]}}, {"patchline": {"destination": ["obj-92", 1], "source": ["obj-96", 0]}}, {"patchline": {"destination": ["obj-96", 0], "source": ["obj-98", 0]}}, {"patchline": {"destination": ["obj-98", 0], "source": ["obj-99", 0]}}], "originid": "pat-632", "parameters": {"obj-54": ["live.gain~", "live.gain~", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "inherited_shortname": 1}, "dependency_cache": [], "autosave": 0}}