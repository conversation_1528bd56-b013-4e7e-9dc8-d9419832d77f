{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [603.0, 164.0, 1000.0, 780.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-28", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [621.0, 305.0, 50.0, 22.0], "text": "360."}}, {"box": {"id": "obj-26", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [632.0, 210.0, 79.0, 22.0], "text": "0, 360 36000"}}, {"box": {"id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["", "bang"], "patching_rect": [632.0, 257.0, 41.0, 22.0], "text": "line 0."}}, {"box": {"id": "obj-21", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [632.0, 139.0, 24.0, 24.0]}}, {"box": {"id": "obj-19", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [382.0, 77.0, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [382.0, 134.0, 69.0, 22.0], "text": "metro 1000"}}, {"box": {"id": "obj-16", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [361.0, 257.0, 50.0, 22.0], "text": "4"}}, {"box": {"id": "obj-14", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [382.0, 180.0, 92.0, 22.0], "text": "counter 0 0 360"}}, {"box": {"id": "obj-12", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [191.0, 348.0, 50.0, 22.0], "text": "3."}}, {"box": {"id": "obj-10", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [177.0, 222.0, 29.5, 22.0], "text": "inc"}}, {"box": {"id": "obj-8", "maxclass": "incdec", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [177.0, 279.0, 20.0, 24.0]}}, {"box": {"id": "obj-7", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [177.0, 104.0, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-5", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [177.0, 171.0, 69.0, 22.0], "text": "metro 1000"}}], "lines": [{"patchline": {"destination": ["obj-8", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-16", 1], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-28", 1], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-10", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-5", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-12", 1], "source": ["obj-8", 0]}}], "originid": "pat-2346", "dependency_cache": [], "autosave": 0}}