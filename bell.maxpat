{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [35.0, 78.0, 1980.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-180", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1798.4617099761963, 1463.0, 73.0, 22.0], "text": "loadmess 1."}}, {"box": {"id": "obj-181", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1968.4617099761963, 1463.0, 80.0, 22.0], "text": "loadmess 70."}}, {"box": {"id": "obj-182", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1872.4617099761963, 1463.0, 93.0, 22.0], "text": "loadmess 3324."}}, {"box": {"id": "obj-175", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1428.2309074401855, 1467.0, 73.0, 22.0], "text": "loadmess 1."}}, {"box": {"id": "obj-178", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1598.2309074401855, 1467.0, 80.0, 22.0], "text": "loadmess 30."}}, {"box": {"id": "obj-179", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1502.2309074401855, 1467.0, 93.0, 22.0], "text": "loadmess 2916."}}, {"box": {"id": "obj-173", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1050.5953834950924, 1473.0, 73.0, 22.0], "text": "loadmess 1."}}, {"box": {"id": "obj-170", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1221.0, 1473.0, 80.0, 22.0], "text": "loadmess 90."}}, {"box": {"id": "obj-171", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1125.0, 1473.0, 93.0, 22.0], "text": "loadmess 3018."}}, {"box": {"id": "obj-169", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [872.0, 1473.0, 80.0, 22.0], "text": "loadmess 84."}}, {"box": {"id": "obj-168", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [776.0, 1473.0, 93.0, 22.0], "text": "loadmess 3096."}}, {"box": {"id": "obj-164", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [701.0, 1473.0, 73.0, 22.0], "text": "loadmess 1."}}, {"box": {"id": "obj-122", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 4, "outlettype": ["signal", "signal", "list", "list"], "patching_rect": [1729.2309341430664, 1944.6155700683594, 120.0, 22.0], "text": "omx.peaklim~ -10 50"}}, {"box": {"id": "obj-124", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [1729.2309341430664, 2000.0001907348633, 45.0, 45.0]}}, {"box": {"id": "obj-125", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 4, "outlettype": ["signal", "signal", "list", "list"], "patching_rect": [1393.8462867736816, 1944.6155700683594, 120.0, 22.0], "text": "omx.peaklim~ -10 50"}}, {"box": {"id": "obj-126", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1750.7693977355957, 1416.9232120513916, 32.0, 22.0], "text": "mtof"}}, {"box": {"format": 6, "id": "obj-127", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1852.3078689575195, 1510.769374847412, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-128", "maxclass": "flonum", "maximum": 100.0, "minimum": 1.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1910.7694129943848, 1509.2309131622314, 52.0, 23.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-129", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1798.4617099761963, 1509.2309131622314, 52.0, 23.0]}}, {"box": {"id": "obj-130", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1750.7693977355957, 1592.3078441619873, 110.38960933685303, 22.0], "text": "reson~"}}, {"box": {"id": "obj-131", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1898.4617195129395, 1590.7693824768066, 24.0, 24.0]}}, {"box": {"id": "obj-132", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [1901.5386428833008, 1764.6155529022217, 34.0, 22.0], "text": "line~"}}, {"box": {"addpoints": [0.0, 0.0, 0, 9.001640563315533, 0.759506009419759, 0, 87.9695770588327, 0.207181574503581, 0, 279.45893876096034, 0.0, 0, 1000.0, 0.0, 0], "classic_curve": 1, "id": "obj-133", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [1898.4617195129395, 1641.5386180877686, 200.0, 100.0]}}, {"box": {"id": "obj-134", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1750.7693977355957, 1655.3847732543945, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-135", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [1741.5386276245117, 1700.0001621246338, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[2]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[2]"}}, {"box": {"id": "obj-136", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1750.7693977355957, 1510.769374847412, 43.0, 22.0], "text": "cycle~"}}, {"box": {"id": "obj-137", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1406.153980255127, 1416.9232120513916, 32.0, 22.0], "text": "mtof"}}, {"box": {"format": 6, "id": "obj-138", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1503.0770664215088, 1509.2309131622314, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-139", "maxclass": "flonum", "maximum": 100.0, "minimum": 1.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1560.0001487731934, 1509.2309131622314, 52.0, 23.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-140", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1449.2309074401855, 1509.2309131622314, 52.0, 23.0]}}, {"box": {"id": "obj-141", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1403.0770568847656, 1592.3078441619873, 110.38960933685303, 22.0], "text": "reson~"}}, {"box": {"id": "obj-142", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1547.692455291748, 1590.7693824768066, 24.0, 24.0]}}, {"box": {"id": "obj-144", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [1570.769380569458, 1778.4617080688477, 34.0, 22.0], "text": "line~"}}, {"box": {"addpoints": [0.0, 0.0, 0, 17.18417634355261, 0.740514907836914, 0, 65.48798845169392, 0.196273272832235, 0, 145.27522249424712, 0.0, 0, 1000.0, 0.0, 0], "classic_curve": 1, "id": "obj-148", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [1510.769374847412, 1641.5386180877686, 200.0, 100.0]}}, {"box": {"id": "obj-156", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1403.0770568847656, 1653.8463115692139, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-158", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [1393.8462867736816, 2000.0001907348633, 45.0, 45.0]}}, {"box": {"id": "obj-162", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [1393.8462867736816, 1698.4617004394531, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[3]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[3]"}}, {"box": {"id": "obj-163", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1403.0770568847656, 1509.2309131622314, 43.0, 22.0], "text": "cycle~"}}, {"box": {"id": "obj-118", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 4, "outlettype": ["signal", "signal", "list", "list"], "patching_rect": [1002.3847122192383, 1950.0, 120.0, 22.0], "text": "omx.peaklim~ -10 50"}}, {"box": {"id": "obj-119", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [1002.3847122192383, 2005.0, 45.0, 45.0]}}, {"box": {"id": "obj-117", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 4, "outlettype": ["signal", "signal", "list", "list"], "patching_rect": [666.0, 1950.0, 120.0, 22.0], "text": "omx.peaklim~ -10 50"}}, {"box": {"id": "obj-103", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [442.0, 1757.0, 40.0, 22.0], "text": "clip~"}}, {"box": {"id": "obj-53", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1023.0770206451416, 1421.4246282577515, 32.0, 22.0], "text": "mtof"}}, {"box": {"format": 6, "id": "obj-57", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1125.5953834950924, 1515.270791053772, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-61", "maxclass": "flonum", "maximum": 100.0, "minimum": 1.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1183.0770359039307, 1514.770791053772, 52.0, 23.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-69", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1071.5953834950924, 1514.770791053772, 52.0, 23.0]}}, {"box": {"id": "obj-70", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1023.0770206451416, 1596.8092603683472, 110.38960933685303, 22.0], "text": "reson~"}}, {"box": {"id": "obj-71", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1170.7693424224854, 1595.8092603683472, 24.0, 24.0]}}, {"box": {"id": "obj-73", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [1173.8462657928467, 1769.1169691085815, 34.0, 22.0], "text": "line~"}}, {"box": {"addpoints": [0.0, 0.0, 0, 9.001640563315533, 0.759506009419759, 0, 65.48798845169392, 0.196273272832235, 0, 221.76761830106696, 0.0, 0, 1000.0, 0.0, 0], "classic_curve": 1, "id": "obj-77", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [1170.7693424224854, 1647.0, 200.0, 100.0]}}, {"box": {"id": "obj-81", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1023.0770206451416, 1659.8861894607544, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-97", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [1013.8462505340576, 1704.5015783309937, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[1]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[1]"}}, {"box": {"id": "obj-100", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1023.0770206451416, 1515.270791053772, 43.0, 22.0], "text": "cycle~"}}, {"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [679.0123999118805, 1422.0, 32.0, 22.0], "text": "mtof"}}, {"box": {"format": 6, "id": "obj-24", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [776.0, 1515.0, 50.0, 22.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-8", "maxclass": "flonum", "maximum": 100.0, "minimum": 1.0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [832.0, 1515.0, 52.0, 23.0]}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "format": 6, "id": "obj-18", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [722.0, 1514.5, 52.0, 23.0]}}, {"box": {"id": "obj-51", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [2108.27, 111.26, 70.0, 22.0], "text": "loadmess 1"}}, {"box": {"id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [675.0, 1597.0, 110.38960933685303, 22.0], "text": "reson~"}}, {"box": {"id": "obj-37", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [820.000078201294, 1596.0, 24.0, 24.0]}}, {"box": {"id": "obj-30", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [843.3333333333334, 1783.0, 34.0, 22.0], "text": "line~"}}, {"box": {"addpoints": [0.0, 0.0, 0, 0.0, 0.751792729695638, 0, 65.48798845169392, 0.196273272832235, 0, 145.27522249424712, 0.0, 0, 1000.0, 0.0, 0], "classic_curve": 1, "id": "obj-28", "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [783.0, 1647.0, 200.0, 100.0]}}, {"box": {"id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [675.0, 1659.0, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-22", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [666.0, 2005.0, 45.0, 45.0]}}, {"box": {"id": "obj-21", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [666.0, 1704.0, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~"}}, {"box": {"format": 6, "id": "obj-20", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [476.0, 1483.0, 50.0, 22.0]}}, {"box": {"id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [675.0, 1515.0, 43.0, 22.0], "text": "cycle~"}}, {"box": {"id": "obj-243", "maxclass": "preset", "numinlets": 1, "numoutlets": 5, "outlettype": ["preset", "int", "preset", "int", ""], "patching_rect": [2102.68, 163.77, 100.0, 40.0], "preset_data": [{"number": 1, "data": [5, "obj-16", "toggle", "int", 1, 5, "obj-27", "number", "int", 51, 5, "obj-33", "number", "float", 4000.0, 5, "obj-40", "toggle", "int", 1, 5, "obj-38", "number", "int", 49, 5, "obj-34", "number", "float", 4000.0, 5, "obj-55", "toggle", "int", 1, 5, "obj-52", "number", "int", 69, 5, "obj-48", "number", "float", 4000.0, 5, "obj-66", "toggle", "int", 1, 5, "obj-64", "number", "int", 100, 5, "obj-60", "number", "float", 4000.0, 5, "obj-98", "number", "int", 0, 5, "obj-106", "number", "int", 2, 5, "obj-110", "number", "int", 0, 5, "obj-114", "number", "int", 0, 5, "obj-143", "number", "int", 120, 5, "obj-145", "number", "int", 85, 5, "obj-151", "number", "int", 84, 5, "obj-157", "number", "int", 97, 5, "obj-185", "number", "float", 4000.0, 4, "obj-187", "function", "clear", 8, "obj-187", "function", "add_with_curve", 0.0, 0.0, 0, 0.0, 8, "obj-187", "function", "add_with_curve", 407.34826315296914, 1.0, 0, -0.15, 8, "obj-187", "function", "add_with_curve", 2518.2565148714802, 1.0, 0, 0.0, 8, "obj-187", "function", "add_with_curve", 10000.0, 0.0, 0, -0.4, 5, "obj-187", "function", "domain", 10000.0, 6, "obj-187", "function", "range", 0.0, 1.0, 5, "obj-187", "function", "mode", 1, 5, "obj-192", "number", "float", 0.0, 5, "obj-201", "number", "int", 10000, 5, "obj-212", "number", "float", 4000.0, 4, "obj-211", "function", "clear", 8, "obj-211", "function", "add_with_curve", 0.0, 0.0, 0, 0.0, 8, "obj-211", "function", "add_with_curve", 806.7092837483631, 0.817959172772304, 0, -0.15, 8, "obj-211", "function", "add_with_curve", 2518.2565148714802, 1.0, 0, 0.0, 8, "obj-211", "function", "add_with_curve", 10000.0, 0.0, 0, -0.4, 5, "obj-211", "function", "domain", 10000.0, 6, "obj-211", "function", "range", 0.0, 1.0, 5, "obj-211", "function", "mode", 1, 5, "obj-207", "number", "float", 0.0, 5, "obj-204", "number", "int", 10000]}]}}, {"box": {"id": "obj-214", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [838.2716718912125, 55.55555999279022, 24.0, 24.0]}}, {"box": {"id": "obj-203", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [1435.3847522735596, 432.95000000000005, 117.0, 22.0], "text": "scale 1. 0. 50. 4000."}}, {"box": {"id": "obj-204", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1270.7693519592285, 36.92308044433594, 50.0, 22.0]}}, {"box": {"id": "obj-205", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1270.7693519592285, 83.07693099975586, 81.0, 22.0], "text": "setdomain $1"}}, {"box": {"id": "obj-206", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1233.8462715148926, 83.07693099975586, 24.0, 24.0]}}, {"box": {"format": 6, "id": "obj-207", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1433.846290588379, 387.69234466552734, 50.0, 22.0]}}, {"box": {"id": "obj-208", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [1433.846290588379, 355.3846492767334, 64.0, 22.0], "text": "snapshot~"}}, {"box": {"id": "obj-209", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1289.2308921813965, 313.84618377685547, 106.0, 22.0], "text": "metro 1 @active 1"}}, {"box": {"id": "obj-210", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [1433.846290588379, 313.84618377685547, 58.0, 22.0], "text": "curve~ 0."}}, {"box": {"addpoints_with_curve": [0.0, 0.0, 0, 0.0, 806.7092837483631, 0.817959172772304, 0, -0.15, 2518.2565148714802, 1.0, 0, 0.0, 10000.0, 0.0, 0, -0.4], "classic_curve": 1, "domain": 10000.0, "id": "obj-211", "maxclass": "function", "mode": 1, "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [1266.1872046589851, 140.00001335144043, 228.3950799703598, 130.86420798301697]}}, {"box": {"format": 6, "id": "obj-212", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1373.846284866333, 479.10385055541997, 50.0, 22.0]}}, {"box": {"id": "obj-202", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": [""], "patching_rect": [776.78, 432.95, 117.0, 22.0], "text": "scale 1. 0. 50. 4000."}}, {"box": {"id": "obj-201", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [631.4136319160461, 51.851855993270874, 50.0, 22.0]}}, {"box": {"id": "obj-199", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [631.4136319160461, 98.53087198734283, 81.0, 22.0], "text": "setdomain $1"}}, {"box": {"id": "obj-194", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [593.8272079229355, 97.53087198734283, 24.0, 24.0]}}, {"box": {"format": 6, "id": "obj-192", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [677.205815911293, 397.53089594841003, 50.0, 22.0]}}, {"box": {"id": "obj-190", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [677.205815911293, 365.4321279525757, 64.0, 22.0], "text": "snapshot~"}}, {"box": {"id": "obj-189", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [532.0988079309464, 324.69138395786285, 106.0, 22.0], "text": "metro 1 @active 1"}}, {"box": {"id": "obj-188", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [677.205815911293, 324.69138395786285, 58.0, 22.0], "text": "curve~ 0."}}, {"box": {"addpoints_with_curve": [0.0, 0.0, 0, 0.0, 407.34826315296914, 1.0, 0, -0.15, 2518.2565148714802, 1.0, 0, 0.0, 10000.0, 0.0, 0, -0.4], "classic_curve": 1, "domain": 10000.0, "id": "obj-187", "maxclass": "function", "mode": 1, "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [607.4074559211731, 167.90124797821045, 228.3950799703598, 130.86420798301697]}}, {"box": {"format": 6, "id": "obj-185", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [716.049439907074, 477.7778159379959, 50.0, 22.0]}}, {"box": {"id": "obj-177", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [1669.2309284210205, 1278.4616603851318, 101.0, 22.0], "text": "makenote 96 120"}}, {"box": {"id": "obj-176", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [1406.153980255127, 1278.4616603851318, 101.0, 22.0], "text": "makenote 96 120"}}, {"box": {"id": "obj-174", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [1027.7718253135681, 1281.5385837554932, 101.0, 22.0], "text": "makenote 96 120"}}, {"box": {"id": "obj-167", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 0, "patching_rect": [476.5, 1397.0, 49.0, 22.0], "text": "noteout"}}, {"box": {"id": "obj-172", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [684.389609336853, 1284.6155071258545, 101.0, 22.0], "text": "makenote 96 120"}}, {"box": {"id": "obj-157", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1758.461706161499, 1008.6553583145142, 50.0, 22.0]}}, {"box": {"id": "obj-159", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1686.1540069580078, 1024.0399751663208, 24.0, 24.0]}}, {"box": {"id": "obj-160", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1686.1540069580078, 1074.8092107772827, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-161", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1603.077075958252, 1208.6553773880005, 50.0, 22.0], "text": "88 57"}}, {"box": {"id": "obj-165", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1633.8463096618652, 1130.1938314437866, 34.0, 22.0], "text": "pack"}}, {"box": {"id": "obj-151", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1510.769374847412, 1008.6553583145142, 50.0, 22.0]}}, {"box": {"id": "obj-152", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1438.461675643921, 1024.0399751663208, 24.0, 24.0]}}, {"box": {"id": "obj-153", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1438.461675643921, 1074.8092107772827, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-154", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1355.384744644165, 1208.6553773880005, 50.0, 22.0], "text": "111 61"}}, {"box": {"id": "obj-155", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1386.1539783477783, 1130.1938314437866, 34.0, 22.0], "text": "pack"}}, {"box": {"id": "obj-145", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1133.8462619781494, 1009.1168966293335, 50.0, 22.0]}}, {"box": {"id": "obj-146", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1052.3077926635742, 1024.5015134811401, 24.0, 24.0]}}, {"box": {"id": "obj-147", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1060.0001010894775, 1075.270749092102, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-149", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [976.9231700897217, 1209.1169157028198, 50.0, 22.0], "text": "99 50"}}, {"box": {"id": "obj-150", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1007.692403793335, 1130.655369758606, 34.0, 22.0], "text": "pack"}}, {"box": {"id": "obj-143", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [802.4691998958588, 1009.8766238689423, 50.0, 22.0]}}, {"box": {"id": "obj-123", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [729.6296879053116, 1024.6914398670197, 24.0, 24.0]}}, {"box": {"id": "obj-121", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [729.6296879053116, 1075.3087278604507, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-120", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [623.4568399190903, 1198.0, 50.0, 22.0], "text": "87 71"}}, {"box": {"id": "obj-116", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [677.7778319120407, 1130.864287853241, 34.0, 22.0], "text": "pack"}}, {"box": {"id": "obj-112", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1807.6924800872803, 784.0399522781372, 24.0, 24.0]}}, {"box": {"id": "obj-113", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1807.6924800872803, 817.8861093521118, 59.0, 22.0], "text": "random 3"}}, {"box": {"id": "obj-114", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1807.6924800872803, 856.3476514816284, 50.0, 22.0]}}, {"box": {"id": "obj-115", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1684.6155452728271, 890.193808555603, 30.0, 22.0], "text": "* 12"}}, {"box": {"id": "obj-108", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1553.8463020324707, 780.9630289077759, 24.0, 24.0]}}, {"box": {"id": "obj-109", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1553.8463020324707, 814.8091859817505, 59.0, 22.0], "text": "random 3"}}, {"box": {"id": "obj-110", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1553.8463020324707, 853.2707281112671, 50.0, 22.0]}}, {"box": {"id": "obj-111", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1429.230905532837, 890.193808555603, 30.0, 22.0], "text": "* 12"}}, {"box": {"id": "obj-104", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1190.769344329834, 781.4245672225952, 24.0, 24.0]}}, {"box": {"id": "obj-105", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1190.769344329834, 815.2707242965698, 59.0, 22.0], "text": "random 3"}}, {"box": {"id": "obj-106", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1190.769344329834, 853.7322664260864, 50.0, 22.0]}}, {"box": {"id": "obj-107", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1073.8462562561035, 890.6553468704224, 30.0, 22.0], "text": "* 12"}}, {"box": {"id": "obj-101", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [872.8395758867264, 783.950679898262, 24.0, 24.0]}}, {"box": {"id": "obj-99", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [872.8395758867264, 817.2840158939362, 59.0, 22.0], "text": "random 3"}}, {"box": {"id": "obj-98", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [872.8395758867264, 856.7901918888092, 50.0, 22.0]}}, {"box": {"id": "obj-96", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [755.4538412988186, 898.0, 30.0, 22.0], "text": "* 12"}}, {"box": {"id": "obj-95", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1664.6155433654785, 916.3476572036743, 29.5, 22.0], "text": "+"}}, {"box": {"id": "obj-94", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1418.4616737365723, 917.886118888855, 29.5, 22.0], "text": "+"}}, {"box": {"id": "obj-93", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1056.9231777191162, 918.3476572036743, 29.5, 22.0], "text": "+"}}, {"box": {"id": "obj-92", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [738.2716639041901, 930.8642718791962, 29.5, 22.0], "text": "+"}}, {"box": {"id": "obj-88", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1664.6155433654785, 814.8091859817505, 24.0, 24.0]}}, {"box": {"id": "obj-89", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1664.6155433654785, 856.3476514816284, 129.0, 22.0], "text": "random @range 84 96"}}, {"box": {"id": "obj-86", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1418.4616737365723, 807.1168775558472, 24.0, 24.0]}}, {"box": {"id": "obj-87", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1418.4616737365723, 853.2707281112671, 129.0, 22.0], "text": "random @range 84 96"}}, {"box": {"id": "obj-84", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1052.3077926635742, 815.2707242965698, 24.0, 24.0]}}, {"box": {"id": "obj-85", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1052.3077926635742, 853.7322664260864, 129.0, 22.0], "text": "random @range 84 96"}}, {"box": {"id": "obj-82", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [738.2716639041901, 817.2840158939362, 24.0, 24.0]}}, {"box": {"id": "obj-80", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [738.2716639041901, 856.7901918888092, 129.0, 22.0], "text": "random @range 84 96"}}, {"box": {"id": "obj-79", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1633.8463096618652, 959.4245843887329, 50.0, 22.0], "text": "88"}}, {"box": {"id": "obj-78", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1384.6155166625977, 957.8861227035522, 50.0, 22.0], "text": "110"}}, {"box": {"id": "obj-76", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1004.6154804229736, 958.3476610183716, 50.0, 22.0], "text": "99"}}, {"box": {"id": "obj-75", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [623.4568399190903, 834.5679678916931, 31.111112594604492, 22.0], "text": "int"}}, {"box": {"id": "obj-74", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [677.7778319120407, 960.493903875351, 50.0, 22.0], "text": "114"}}, {"box": {"format": 6, "id": "obj-60", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1675.3847751617432, 564.0399312973022, 50.0, 22.0]}}, {"box": {"id": "obj-62", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1633.8463096618652, 827.1168794631958, 24.0, 24.0]}}, {"box": {"id": "obj-63", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [1633.8463096618652, 777.8861055374146, 34.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-64", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1801.5386333465576, 657.8860940933228, 50.0, 22.0]}}, {"box": {"id": "obj-65", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1636.9232330322266, 699.4245595932007, 182.8828827738762, 22.0], "text": "<"}}, {"box": {"id": "obj-66", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [1640.000156402588, 564.0399312973022, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-67", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [1690.7693920135498, 591.7322416305542, 150.0, 20.0], "text": "wind velocity"}}, {"box": {"id": "obj-68", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1638.4616947174072, 616.3476285934448, 56.0, 22.0], "text": "metro 50"}}, {"box": {"id": "obj-72", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1638.4616947174072, 654.8091707229614, 73.0, 22.0], "text": "random 100"}}, {"box": {"format": 6, "id": "obj-48", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1421.5385971069336, 562.5014696121216, 50.0, 22.0]}}, {"box": {"id": "obj-49", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1384.6155166625977, 825.5784177780151, 24.0, 24.0]}}, {"box": {"id": "obj-50", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [1384.6155166625977, 774.8091821670532, 34.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-52", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1547.692455291748, 654.8091707229614, 50.0, 22.0]}}, {"box": {"id": "obj-54", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1384.6155166625977, 697.88609790802, 182.8828827738762, 22.0], "text": "<"}}, {"box": {"id": "obj-55", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [1386.1539783477783, 560.9630079269409, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-56", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [1438.461675643921, 590.1937799453735, 150.0, 20.0], "text": "wind velocity"}}, {"box": {"id": "obj-58", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1384.6155166625977, 614.8091669082642, 56.0, 22.0], "text": "metro 50"}}, {"box": {"id": "obj-59", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1384.6155166625977, 653.2707090377808, 73.0, 22.0], "text": "random 100"}}, {"box": {"format": 6, "id": "obj-34", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1044.615484237671, 562.9630079269409, 50.0, 22.0]}}, {"box": {"id": "obj-35", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [1004.6154804229736, 826.0399560928345, 24.0, 24.0]}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [1004.6154804229736, 775.2707204818726, 34.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-38", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1170.7693424224854, 656.8091707229614, 50.0, 22.0]}}, {"box": {"id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [1006.1539421081543, 698.3476362228394, 182.8828827738762, 22.0], "text": "<"}}, {"box": {"id": "obj-40", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [1007.692403793335, 561.4245462417603, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-42", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [1060.0001010894775, 590.6553182601929, 150.0, 20.0], "text": "wind velocity"}}, {"box": {"id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1007.692403793335, 615.2707052230835, 56.0, 22.0], "text": "metro 50"}}, {"box": {"id": "obj-46", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1007.692403793335, 653.7322473526001, 73.0, 22.0], "text": "random 100"}}, {"box": {"format": 6, "id": "obj-33", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [716.049439907074, 562.9630079269409, 50.0, 22.0]}}, {"box": {"id": "obj-31", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [677.7778319120407, 828.3951278924942, 24.0, 24.0]}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [677.7778319120407, 777.7778398990631, 34.0, 22.0], "text": "sel 1"}}, {"box": {"id": "obj-27", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [841.9753758907318, 656.7901759147644, 50.0, 22.0]}}, {"box": {"id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [679.0123999118805, 698.765487909317, 182.8828827738762, 22.0], "text": "<"}}, {"box": {"id": "obj-16", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [680.2469679117203, 561.7284399271011, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-14", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [732.0988239049911, 591.3580719232559, 150.0, 20.0], "text": "wind velocity"}}, {"box": {"id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [679.0123999118805, 616.0494319200516, 56.0, 22.0], "text": "metro 50"}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [679.0123999118805, 653.086471915245, 73.0, 22.0], "text": "random 100"}}, {"box": {"id": "obj-91", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [1922.2223757505417, 429.6296639442444, 150.0, 20.0], "text": "wind chimes"}}, {"box": {"id": "obj-90", "linecount": 7, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [1632.0988957881927, 345.67903995513916, 255.19480276107788, 105.0], "text": "风铃效果\n\n音高随机\n力度随机\n声音触发随机\n\n模拟风的强度 强风吹过 -> 归于平静"}}], "lines": [{"patchline": {"destination": ["obj-70", 0], "source": ["obj-100", 0]}}, {"patchline": {"destination": ["obj-99", 0], "source": ["obj-101", 0]}}, {"patchline": {"destination": ["obj-105", 0], "source": ["obj-104", 0]}}, {"patchline": {"destination": ["obj-106", 0], "source": ["obj-105", 0]}}, {"patchline": {"destination": ["obj-107", 0], "source": ["obj-106", 0]}}, {"patchline": {"destination": ["obj-93", 1], "source": ["obj-107", 0]}}, {"patchline": {"destination": ["obj-109", 0], "source": ["obj-108", 0]}}, {"patchline": {"destination": ["obj-110", 0], "source": ["obj-109", 0]}}, {"patchline": {"destination": ["obj-111", 0], "source": ["obj-110", 0]}}, {"patchline": {"destination": ["obj-94", 1], "source": ["obj-111", 0]}}, {"patchline": {"destination": ["obj-113", 0], "source": ["obj-112", 0]}}, {"patchline": {"destination": ["obj-114", 0], "source": ["obj-113", 0]}}, {"patchline": {"destination": ["obj-115", 0], "source": ["obj-114", 0]}}, {"patchline": {"destination": ["obj-95", 1], "source": ["obj-115", 0]}}, {"patchline": {"destination": ["obj-120", 1], "order": 1, "source": ["obj-116", 0]}}, {"patchline": {"destination": ["obj-172", 0], "order": 0, "source": ["obj-116", 0]}}, {"patchline": {"destination": ["obj-22", 1], "source": ["obj-117", 1]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-117", 0]}}, {"patchline": {"destination": ["obj-119", 1], "source": ["obj-118", 1]}}, {"patchline": {"destination": ["obj-119", 0], "source": ["obj-118", 0]}}, {"patchline": {"destination": ["obj-116", 1], "source": ["obj-121", 0]}}, {"patchline": {"destination": ["obj-124", 1], "source": ["obj-122", 1]}}, {"patchline": {"destination": ["obj-124", 0], "source": ["obj-122", 0]}}, {"patchline": {"destination": ["obj-121", 0], "source": ["obj-123", 0]}}, {"patchline": {"destination": ["obj-158", 1], "source": ["obj-125", 1]}}, {"patchline": {"destination": ["obj-158", 0], "source": ["obj-125", 0]}}, {"patchline": {"destination": ["obj-131", 0], "midpoints": [1760.2693977355957, 1447.7325086593628, 1907.9617195129395, 1447.7325086593628], "order": 0, "source": ["obj-126", 0]}}, {"patchline": {"destination": ["obj-136", 0], "order": 1, "source": ["obj-126", 0]}}, {"patchline": {"destination": ["obj-130", 2], "source": ["obj-127", 0]}}, {"patchline": {"destination": ["obj-130", 3], "source": ["obj-128", 0]}}, {"patchline": {"destination": ["obj-130", 1], "source": ["obj-129", 0]}}, {"patchline": {"destination": ["obj-134", 0], "source": ["obj-130", 0]}}, {"patchline": {"destination": ["obj-133", 0], "source": ["obj-131", 0]}}, {"patchline": {"destination": ["obj-134", 1], "source": ["obj-132", 0]}}, {"patchline": {"destination": ["obj-132", 0], "source": ["obj-133", 1]}}, {"patchline": {"destination": ["obj-135", 1], "order": 0, "source": ["obj-134", 0]}}, {"patchline": {"destination": ["obj-135", 0], "order": 1, "source": ["obj-134", 0]}}, {"patchline": {"destination": ["obj-122", 1], "source": ["obj-135", 1]}}, {"patchline": {"destination": ["obj-122", 0], "source": ["obj-135", 0]}}, {"patchline": {"destination": ["obj-130", 0], "source": ["obj-136", 0]}}, {"patchline": {"destination": ["obj-142", 0], "midpoints": [1415.653980255127, 1447.7309131622314, 1557.192455291748, 1447.7309131622314], "order": 0, "source": ["obj-137", 0]}}, {"patchline": {"destination": ["obj-163", 0], "order": 1, "source": ["obj-137", 0]}}, {"patchline": {"destination": ["obj-141", 2], "source": ["obj-138", 0]}}, {"patchline": {"destination": ["obj-141", 3], "source": ["obj-139", 0]}}, {"patchline": {"destination": ["obj-141", 1], "source": ["obj-140", 0]}}, {"patchline": {"destination": ["obj-156", 0], "source": ["obj-141", 0]}}, {"patchline": {"destination": ["obj-148", 0], "source": ["obj-142", 0]}}, {"patchline": {"destination": ["obj-121", 1], "source": ["obj-143", 0]}}, {"patchline": {"destination": ["obj-156", 1], "source": ["obj-144", 0]}}, {"patchline": {"destination": ["obj-147", 1], "source": ["obj-145", 0]}}, {"patchline": {"destination": ["obj-147", 0], "source": ["obj-146", 0]}}, {"patchline": {"destination": ["obj-150", 1], "source": ["obj-147", 0]}}, {"patchline": {"destination": ["obj-144", 0], "source": ["obj-148", 1]}}, {"patchline": {"destination": ["obj-149", 1], "order": 1, "source": ["obj-150", 0]}}, {"patchline": {"destination": ["obj-174", 0], "order": 0, "source": ["obj-150", 0]}}, {"patchline": {"destination": ["obj-153", 1], "source": ["obj-151", 0]}}, {"patchline": {"destination": ["obj-153", 0], "source": ["obj-152", 0]}}, {"patchline": {"destination": ["obj-155", 1], "source": ["obj-153", 0]}}, {"patchline": {"destination": ["obj-154", 1], "order": 1, "source": ["obj-155", 0]}}, {"patchline": {"destination": ["obj-176", 0], "order": 0, "source": ["obj-155", 0]}}, {"patchline": {"destination": ["obj-162", 1], "order": 0, "source": ["obj-156", 0]}}, {"patchline": {"destination": ["obj-162", 0], "order": 1, "source": ["obj-156", 0]}}, {"patchline": {"destination": ["obj-160", 1], "source": ["obj-157", 0]}}, {"patchline": {"destination": ["obj-160", 0], "source": ["obj-159", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-165", 1], "source": ["obj-160", 0]}}, {"patchline": {"destination": ["obj-125", 1], "source": ["obj-162", 1]}}, {"patchline": {"destination": ["obj-125", 0], "source": ["obj-162", 0]}}, {"patchline": {"destination": ["obj-141", 0], "source": ["obj-163", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-164", 0]}}, {"patchline": {"destination": ["obj-161", 1], "order": 1, "source": ["obj-165", 0]}}, {"patchline": {"destination": ["obj-177", 0], "order": 0, "source": ["obj-165", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-168", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-169", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-61", 0], "source": ["obj-170", 0]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-171", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-172", 1]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-172", 0]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-173", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-174", 1]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-174", 0]}}, {"patchline": {"destination": ["obj-140", 0], "source": ["obj-175", 0]}}, {"patchline": {"destination": ["obj-137", 0], "source": ["obj-176", 1]}}, {"patchline": {"destination": ["obj-137", 0], "source": ["obj-176", 0]}}, {"patchline": {"destination": ["obj-126", 0], "source": ["obj-177", 1]}}, {"patchline": {"destination": ["obj-126", 0], "source": ["obj-177", 0]}}, {"patchline": {"destination": ["obj-139", 0], "source": ["obj-178", 0]}}, {"patchline": {"destination": ["obj-138", 0], "source": ["obj-179", 0]}}, {"patchline": {"destination": ["obj-41", 1], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-129", 0], "source": ["obj-180", 0]}}, {"patchline": {"destination": ["obj-128", 0], "source": ["obj-181", 0]}}, {"patchline": {"destination": ["obj-127", 0], "source": ["obj-182", 0]}}, {"patchline": {"destination": ["obj-33", 0], "order": 1, "source": ["obj-185", 0]}}, {"patchline": {"destination": ["obj-34", 0], "order": 0, "source": ["obj-185", 0]}}, {"patchline": {"destination": ["obj-188", 0], "source": ["obj-187", 1]}}, {"patchline": {"destination": ["obj-190", 0], "source": ["obj-188", 0]}}, {"patchline": {"destination": ["obj-190", 0], "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-192", 0], "source": ["obj-190", 0]}}, {"patchline": {"destination": ["obj-202", 0], "source": ["obj-192", 0]}}, {"patchline": {"destination": ["obj-187", 0], "source": ["obj-194", 0]}}, {"patchline": {"destination": ["obj-187", 0], "source": ["obj-199", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-199", 0], "source": ["obj-201", 0]}}, {"patchline": {"destination": ["obj-185", 0], "source": ["obj-202", 0]}}, {"patchline": {"destination": ["obj-212", 0], "source": ["obj-203", 0]}}, {"patchline": {"destination": ["obj-205", 0], "source": ["obj-204", 0]}}, {"patchline": {"destination": ["obj-211", 0], "source": ["obj-205", 0]}}, {"patchline": {"destination": ["obj-211", 0], "source": ["obj-206", 0]}}, {"patchline": {"destination": ["obj-203", 0], "source": ["obj-207", 0]}}, {"patchline": {"destination": ["obj-207", 0], "source": ["obj-208", 0]}}, {"patchline": {"destination": ["obj-208", 0], "source": ["obj-209", 0]}}, {"patchline": {"destination": ["obj-117", 1], "source": ["obj-21", 1]}}, {"patchline": {"destination": ["obj-117", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-208", 0], "source": ["obj-210", 0]}}, {"patchline": {"destination": ["obj-210", 0], "source": ["obj-211", 1]}}, {"patchline": {"destination": ["obj-48", 0], "order": 1, "source": ["obj-212", 0]}}, {"patchline": {"destination": ["obj-60", 0], "order": 0, "source": ["obj-212", 0]}}, {"patchline": {"destination": ["obj-194", 0], "order": 1, "source": ["obj-214", 0]}}, {"patchline": {"destination": ["obj-206", 0], "order": 0, "source": ["obj-214", 0]}}, {"patchline": {"destination": ["obj-21", 1], "order": 0, "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-21", 0], "order": 1, "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-41", 2], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-19", 1], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-28", 1]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-23", 1], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-4", 1], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-43", 1], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-76", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-39", 1], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-101", 0], "order": 0, "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-123", 0], "order": 2, "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-2", 0], "order": 3, "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-82", 0], "order": 1, "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-43", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-104", 0], "order": 0, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-146", 0], "order": 1, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-46", 0], "order": 3, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-84", 0], "order": 2, "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-17", 0], "order": 1, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-37", 0], "midpoints": [688.5123999118805, 1453.5, 829.500078201294, 1453.5], "order": 0, "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-58", 1], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-78", 0], "source": ["obj-49", 0]}}, {"patchline": {"destination": ["obj-49", 0], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-243", 0], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-54", 1], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-100", 0], "order": 1, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-71", 0], "midpoints": [1032.5770206451416, 1453.5015954971313, 1180.2693424224854, 1453.5015954971313], "order": 0, "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-58", 0], "source": ["obj-55", 0]}}, {"patchline": {"destination": ["obj-70", 2], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-108", 0], "order": 0, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-152", 0], "order": 1, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-59", 0], "order": 3, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-86", 0], "order": 2, "source": ["obj-58", 0]}}, {"patchline": {"destination": ["obj-54", 0], "source": ["obj-59", 0]}}, {"patchline": {"destination": ["obj-68", 1], "source": ["obj-60", 0]}}, {"patchline": {"destination": ["obj-70", 3], "source": ["obj-61", 0]}}, {"patchline": {"destination": ["obj-79", 0], "source": ["obj-62", 0]}}, {"patchline": {"destination": ["obj-62", 0], "source": ["obj-63", 0]}}, {"patchline": {"destination": ["obj-65", 1], "source": ["obj-64", 0]}}, {"patchline": {"destination": ["obj-63", 0], "source": ["obj-65", 0]}}, {"patchline": {"destination": ["obj-68", 0], "source": ["obj-66", 0]}}, {"patchline": {"destination": ["obj-112", 0], "order": 0, "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-159", 0], "order": 1, "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-72", 0], "order": 3, "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-88", 0], "order": 2, "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-70", 1], "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-81", 0], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-77", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-81", 1], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-116", 0], "source": ["obj-74", 0]}}, {"patchline": {"destination": ["obj-150", 0], "source": ["obj-76", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-77", 1]}}, {"patchline": {"destination": ["obj-155", 0], "source": ["obj-78", 0]}}, {"patchline": {"destination": ["obj-165", 0], "source": ["obj-79", 0]}}, {"patchline": {"destination": ["obj-41", 3], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-92", 0], "source": ["obj-80", 0]}}, {"patchline": {"destination": ["obj-97", 1], "order": 0, "source": ["obj-81", 0]}}, {"patchline": {"destination": ["obj-97", 0], "order": 1, "source": ["obj-81", 0]}}, {"patchline": {"destination": ["obj-80", 0], "source": ["obj-82", 0]}}, {"patchline": {"destination": ["obj-85", 0], "source": ["obj-84", 0]}}, {"patchline": {"destination": ["obj-93", 0], "source": ["obj-85", 0]}}, {"patchline": {"destination": ["obj-87", 0], "source": ["obj-86", 0]}}, {"patchline": {"destination": ["obj-94", 0], "source": ["obj-87", 0]}}, {"patchline": {"destination": ["obj-89", 0], "source": ["obj-88", 0]}}, {"patchline": {"destination": ["obj-95", 0], "source": ["obj-89", 0]}}, {"patchline": {"destination": ["obj-74", 1], "source": ["obj-92", 0]}}, {"patchline": {"destination": ["obj-76", 1], "source": ["obj-93", 0]}}, {"patchline": {"destination": ["obj-78", 1], "source": ["obj-94", 0]}}, {"patchline": {"destination": ["obj-79", 1], "source": ["obj-95", 0]}}, {"patchline": {"destination": ["obj-92", 1], "source": ["obj-96", 0]}}, {"patchline": {"destination": ["obj-118", 1], "source": ["obj-97", 1]}}, {"patchline": {"destination": ["obj-118", 0], "source": ["obj-97", 0]}}, {"patchline": {"destination": ["obj-96", 0], "source": ["obj-98", 0]}}, {"patchline": {"destination": ["obj-98", 0], "source": ["obj-99", 0]}}], "originid": "pat-228", "parameters": {"obj-135": ["live.gain~[2]", "live.gain~", 0], "obj-162": ["live.gain~[3]", "live.gain~", 0], "obj-21": ["live.gain~", "live.gain~", 0], "obj-97": ["live.gain~[1]", "live.gain~", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "inherited_shortname": 1}, "dependency_cache": [], "autosave": 0}}