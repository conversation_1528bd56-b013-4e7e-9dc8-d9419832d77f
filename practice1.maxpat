{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [130.0, 84.0, 1580.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-3", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [490.0, 91.44736754894257, 24.0, 24.0], "presentation": 1, "presentation_rect": [490.0, 91.44736754894257, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-2", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [516.0, 128.75817400217056, 24.0, 24.0]}}, {"box": {"id": "obj-154", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [550.0, 262.0, 24.0, 24.0]}}, {"box": {"id": "obj-92", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [542.0, 552.0, 31.578945994377136, 20.0], "text": "max"}}, {"box": {"id": "obj-90", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [443.55555748939514, 319.0, 44.44444251060486, 20.0], "presentation": 1, "presentation_rect": [479.77777874469757, 69.44736754894257, 44.44444251060486, 20.0], "text": "on/off"}}, {"box": {"id": "obj-88", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [604.2368410825729, 185.0, 50.0, 20.0], "presentation": 1, "presentation_rect": [523.856725692749, 69.44736754894257, 50.0, 20.0], "text": "bpm"}}, {"box": {"id": "obj-79", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [490.0, 578.0, 24.0, 24.0], "presentation": 1, "presentation_rect": [481.75146293640137, 206.28947150707245, 40.13157856464386, 40.13157856464386]}}, {"box": {"id": "obj-73", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [516.0, 368.0, 50.0, 22.0]}}, {"box": {"id": "obj-71", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [516.0, 318.0, 107.23684108257294, 22.0], "text": "/"}}, {"box": {"id": "obj-70", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [604.2368410825729, 207.0, 50.0, 22.0], "presentation": 1, "presentation_rect": [519.9093573093414, 91.15789365768433, 48.026315331459045, 22.0]}}, {"box": {"id": "obj-68", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [516.0, 184.0, 42.0, 22.0], "text": "60000"}}, {"box": {"id": "obj-65", "maxclass": "led", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [546.0, 526.0, 24.0, 24.0], "presentation": 1, "presentation_rect": [523.856725692749, 206.28947150707245, 40.13157856464386, 40.13157856464386]}}, {"box": {"id": "obj-40", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [490.0, 527.0, 50.0, 22.0]}}, {"box": {"id": "obj-38", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [490.0, 317.0, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [490.0, 419.0, 45.0, 22.0], "text": "metro i"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [490.0, 466.0, 69.0, 22.0], "text": "counter 1 8"}}, {"box": {"id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [267.0, 551.0, 40.0, 22.0], "text": "select"}}], "lines": [{"patchline": {"destination": ["obj-71", 0], "source": ["obj-154", 0]}}, {"patchline": {"destination": ["obj-68", 0], "order": 1, "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-70", 0], "order": 0, "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-2", 0], "order": 0, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-38", 0], "order": 1, "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-32", 2]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-79", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-154", 0], "order": 1, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-71", 1], "order": 0, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-36", 1], "source": ["obj-73", 0]}}], "originid": "pat-597", "dependency_cache": [], "autosave": 0, "toolbaradditions": ["BEAP", "packagemanager"]}}