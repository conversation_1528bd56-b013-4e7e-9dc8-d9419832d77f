{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [59.0, 106.0, 1000.0, 780.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 10.0, "id": "obj-51", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [545.0, 205.0, 45.0, 18.0], "text": "Release", "textcolor": [1.0, 1.0, 1.0, 1.0], "textjustification": 1}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 10.0, "id": "obj-50", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [485.0, 205.0, 45.0, 18.0], "text": "<PERSON><PERSON><PERSON>", "textcolor": [1.0, 1.0, 1.0, 1.0], "textjustification": 1}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 10.0, "id": "obj-49", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [325.0, 205.0, 90.0, 18.0], "text": "X  Attack  Y", "textcolor": [1.0, 1.0, 1.0, 1.0], "textjustification": 1}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 10.0, "id": "obj-48", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [425.0, 205.0, 45.0, 18.0], "text": "Decay", "textcolor": [1.0, 1.0, 1.0, 1.0], "textjustification": 1}}, {"box": {"id": "obj-45", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [275.0, 75.0, 45.0, 20.0], "text": "Reset"}}, {"box": {"fontsize": 11.5, "id": "obj-43", "linecount": 11, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [285.0, 440.0, 38.0, 152.0], "text": "1000\n\n800\n\n600\n\n400\n\n200\n\n0", "textjustification": 2}}, {"box": {"fontsize": 11.5, "id": "obj-42", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [320.0, 580.0, 350.0, 20.0], "text": "0     100    200    300   400   500   600   700   800   900   1000"}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [260.0, 230.0, 20.0, 22.0], "text": "t l"}}, {"box": {"id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "patching_rect": [200.0, 190.0, 80.0, 22.0], "text": "route symbol"}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "blinkcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-38", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "outlinecolor": [0.65098, 0.666667, 0.662745, 1.0], "parameter_enable": 0, "patching_rect": [250.0, 75.0, 24.0, 24.0]}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 9.0, "id": "obj-35", "ignoreclick": 1, "maxclass": "number", "maximum": 1000, "minimum": 0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [545.0, 275.0, 30.0, 19.0], "textcolor": [1.0, 1.0, 1.0, 1.0], "triangle": 0}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 9.0, "id": "obj-33", "ignoreclick": 1, "maxclass": "number", "maximum": 1000, "minimum": 0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [485.0, 275.0, 30.0, 19.0], "textcolor": [1.0, 1.0, 1.0, 1.0], "triangle": 0}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 9.0, "id": "obj-34", "ignoreclick": 1, "maxclass": "number", "maximum": 1000, "minimum": 0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [425.0, 275.0, 30.0, 19.0], "textcolor": [1.0, 1.0, 1.0, 1.0], "triangle": 0}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 9.0, "id": "obj-32", "ignoreclick": 1, "maxclass": "number", "maximum": 1000, "minimum": 0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [370.0, 275.0, 30.0, 19.0], "textcolor": [1.0, 1.0, 1.0, 1.0], "triangle": 0}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "fontsize": 9.0, "id": "obj-31", "ignoreclick": 1, "maxclass": "number", "maximum": 1000, "minimum": 0, "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [325.0, 275.0, 30.0, 19.0], "textcolor": [1.0, 1.0, 1.0, 1.0], "triangle": 0}}, {"box": {"id": "obj-30", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 5, "outlettype": ["int", "int", "int", "int", "int"], "patching_rect": [325.0, 160.0, 129.0, 22.0], "text": "t 150 800 300 500 800"}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 3, "outlettype": ["bang", "bang", "int"], "patching_rect": [180.0, 125.0, 40.0, 22.0], "text": "uzi 6"}}, {"box": {"color": [0.92549, 0.364706, 0.341176, 1.0], "id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [180.0, 75.0, 58.0, 22.0], "text": "loadbang"}}, {"box": {"color": [0.317647, 0.654902, 0.976471, 1.0], "id": "obj-27", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 4, "outlettype": ["", "", "", ""], "patching_rect": [200.0, 160.0, 50.5, 22.0], "saved_object_attributes": {"embed": 0, "precision": 6}, "text": "coll"}}, {"box": {"addpoints": [0.0, 0.0, 0, 97.0, 860.0, 0, 313.0, 521.0, 0, 800.0, 521.0, 0, 1000.0, 0.0, 0], "bgcolor": [0.0, 0.0, 0.0, 1.0], "classic_curve": 1, "clickadd": 0, "clicksustain": 0, "grid": 3, "gridstep_y": 100.0, "id": "obj-26", "ignoreclick": 1, "legend": 0, "linecolor": [0.952941, 0.564706, 0.098039, 1.0], "linethickness": 2.0, "maxclass": "function", "numinlets": 1, "numoutlets": 4, "outlettype": ["float", "", "", "bang"], "parameter_enable": 0, "patching_rect": [325.0, 440.0, 305.0, 140.0], "range": [0.0, 1000.0]}}, {"box": {"id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [545.0, 345.0, 63.0, 22.0], "text": "prepend 3"}}, {"box": {"id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [545.0, 315.0, 60.0, 22.0], "text": "pak"}}, {"box": {"id": "obj-22", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [425.0, 345.0, 63.0, 22.0], "text": "prepend 2"}}, {"box": {"id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [425.0, 315.0, 80.0, 22.0], "text": "pak"}}, {"box": {"id": "obj-21", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [325.0, 345.0, 63.0, 22.0], "text": "prepend 1"}}, {"box": {"id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [325.0, 315.0, 65.0, 22.0], "text": "pak"}}, {"box": {"id": "obj-17", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [425.0, 205.0, 40.0, 20.0], "text": "2 X", "textjustification": 1}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "id": "obj-11", "maxclass": "dial", "needlecolor": [0.952941, 0.564706, 0.098039, 1.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "outlinecolor": [0.32549, 0.345098, 0.372549, 1.0], "parameter_enable": 0, "patching_rect": [485.0, 225.0, 45.0, 45.0], "size": 1001.0, "thickness": 100.0}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "id": "obj-12", "maxclass": "dial", "needlecolor": [0.952941, 0.564706, 0.098039, 1.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "outlinecolor": [0.32549, 0.345098, 0.372549, 1.0], "parameter_enable": 0, "patching_rect": [370.0, 225.0, 45.0, 45.0], "size": 1001.0, "thickness": 100.0}}, {"box": {"id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [325.0, 395.0, 19.0, 22.0], "text": "t l"}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "id": "obj-6", "maxclass": "dial", "needlecolor": [0.952941, 0.564706, 0.098039, 1.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "outlinecolor": [0.32549, 0.345098, 0.372549, 1.0], "parameter_enable": 0, "patching_rect": [545.0, 225.0, 45.0, 45.0], "size": 1001.0, "thickness": 100.0}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "id": "obj-5", "maxclass": "dial", "needlecolor": [0.952941, 0.564706, 0.098039, 1.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "outlinecolor": [0.32549, 0.345098, 0.372549, 1.0], "parameter_enable": 0, "patching_rect": [425.0, 225.0, 45.0, 45.0], "size": 1001.0, "thickness": 100.0}}, {"box": {"bgcolor": [0.0, 0.0, 0.0, 1.0], "id": "obj-1", "maxclass": "dial", "needlecolor": [0.952941, 0.564706, 0.098039, 1.0], "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "outlinecolor": [0.32549, 0.345098, 0.372549, 1.0], "parameter_enable": 0, "patching_rect": [325.0, 225.0, 45.0, 45.0], "size": 1001.0, "thickness": 100.0}}], "lines": [{"patchline": {"destination": ["obj-31", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-8", 0], "midpoints": [434.5, 380.5, 334.5, 380.5], "source": ["obj-22", 0]}}, {"patchline": {"destination": ["obj-22", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-8", 0], "midpoints": [554.5, 380.5, 334.5, 380.5], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-27", 0], "source": ["obj-29", 2]}}, {"patchline": {"destination": ["obj-30", 0], "midpoints": [200.0, 154.5, 334.5, 154.5], "source": ["obj-29", 1]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-11", 0], "midpoints": [417.0, 193.5, 494.5, 193.5], "source": ["obj-30", 3]}}, {"patchline": {"destination": ["obj-12", 0], "midpoints": [362.0, 190.5, 379.5, 190.5], "source": ["obj-30", 1]}}, {"patchline": {"destination": ["obj-5", 0], "midpoints": [389.5, 197.5, 434.5, 197.5], "source": ["obj-30", 2]}}, {"patchline": {"destination": ["obj-6", 0], "midpoints": [444.5, 186.5, 554.5, 186.5], "source": ["obj-30", 4]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-20", 1], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-23", 1], "order": 1, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-25", 1], "midpoints": [494.5, 304.5, 595.5, 304.5], "order": 0, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-39", 1]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-8", 0], "midpoints": [269.5, 382.5, 334.5, 382.5], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-35", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-8", 0]}}], "originid": "pat-318", "dependency_cache": [], "autosave": 0}}