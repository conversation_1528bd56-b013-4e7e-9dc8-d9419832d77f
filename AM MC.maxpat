{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [35.0, 78.0, 1980.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"id": "obj-44", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 3, "outlettype": ["multichannelsignal", "", ""], "patching_rect": [961.0, 706.0, 111.0, 22.0], "text": "mc.line~ @chans 4"}}, {"box": {"id": "obj-45", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [966.0, 672.0, 111.0, 22.0], "text": "spreadinclusive 0 1"}}, {"box": {"id": "obj-46", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [979.0, 628.0, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [842.0, 810.0, 84.0, 22.0], "text": "mc.unpack~ 2"}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [844.0, 748.0, 140.0, 22.0], "text": "mc.stereo~ @autogain 1"}}, {"box": {"id": "obj-41", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [842.0, 896.0, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[3]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[2]"}}, {"box": {"id": "obj-38", "maxclass": "spectroscope~", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1312.0, 644.0, 300.0, 100.0]}}, {"box": {"id": "obj-34", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [983.5, 295.0, 50.0, 22.0]}}, {"box": {"id": "obj-30", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [822.5, 266.0, 32.0, 22.0], "text": "mtof"}}, {"box": {"id": "obj-29", "maxclass": "kslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["int", "int"], "parameter_enable": 0, "patching_rect": [853.5, 205.0, 336.0, 53.0]}}, {"box": {"id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [1103.0, 470.0, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [983.0, 380.0, 50.0, 22.0], "text": "mc.sig~"}}, {"box": {"id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [972.0, 418.0, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-8", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [736.0, 904.0, 31.0, 22.0], "text": "stop"}}, {"box": {"id": "obj-11", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [1174.0, 431.0, 50.0, 22.0], "text": "mc.sig~"}}, {"box": {"format": 6, "id": "obj-12", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1174.0, 380.0, 50.0, 22.0]}}, {"box": {"id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [972.0, 517.0, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-18", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [648.0, 904.0, 72.0, 22.0], "text": "startwindow"}}, {"box": {"id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [736.0, 1006.0, 35.0, 22.0], "text": "dac~"}}, {"box": {"id": "obj-27", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [864.0, 507.0, 42.0, 22.0], "text": "mc.+~"}}, {"box": {"id": "obj-31", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [864.0, 374.0, 50.0, 22.0], "text": "mc.sig~"}}, {"box": {"format": 6, "id": "obj-33", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [864.5, 295.0, 50.0, 22.0]}}, {"box": {"id": "obj-5", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [804.0, 569.0, 42.857148349285126, 20.0], "text": "Fc"}}, {"box": {"id": "obj-35", "linecount": 2, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [1035.5, 295.0, 54.285721242427826, 34.0], "text": "Fm factor"}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [864.0, 568.0, 120.0, 22.0], "text": "mc.cycle~ @chans 4"}}, {"box": {"id": "obj-37", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [972.0, 470.0, 62.0, 22.0], "text": "mc.cycle~"}}, {"box": {"id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 3, "outlettype": ["multichannelsignal", "", ""], "patching_rect": [476.0, 514.0, 111.0, 22.0], "text": "mc.line~ @chans 4"}}, {"box": {"id": "obj-23", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [481.0, 480.0, 111.0, 22.0], "text": "spreadinclusive 0 1"}}, {"box": {"id": "obj-21", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [494.0, 436.0, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-20", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [498.0, 210.0, 91.0, 22.0], "text": "harmonic 2 200"}}, {"box": {"id": "obj-19", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [331.0, 210.0, 91.0, 22.0], "text": "harmonic 2 220"}}, {"box": {"id": "obj-17", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [349.0, 644.0, 84.0, 22.0], "text": "mc.unpack~ 2"}}, {"box": {"id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [351.0, 582.0, 140.0, 22.0], "text": "mc.stereo~ @autogain 1"}}, {"box": {"id": "obj-15", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [498.0, 347.0, 120.0, 22.0], "text": "mc.scale~ -1. 1. 0. 1."}}, {"box": {"id": "obj-14", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [498.0, 284.0, 120.0, 22.0], "text": "mc.cycle~ @chans 4"}}, {"box": {"id": "obj-32", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [352.5, 765.0, 48.0, 136.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[1]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[1]"}}, {"box": {"id": "obj-9", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [398.5, 921.0, 31.0, 22.0], "text": "stop"}}, {"box": {"id": "obj-7", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [285.5, 916.0, 72.0, 22.0], "text": "startwindow"}}, {"box": {"id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [349.0, 996.0, 35.0, 22.0], "text": "dac~"}}, {"box": {"id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [1722.0, 699.0, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [331.0, 409.0, 40.0, 22.0], "text": "mc.*~"}}, {"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [331.0, 284.0, 120.0, 22.0], "text": "mc.cycle~ @chans 4"}}], "lines": [{"patchline": {"destination": ["obj-2", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-28", 1], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-11", 0], "source": ["obj-12", 0]}}, {"patchline": {"destination": ["obj-27", 1], "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-15", 0], "source": ["obj-14", 0]}}, {"patchline": {"destination": ["obj-2", 1], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-17", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-32", 1], "source": ["obj-17", 1]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-16", 1], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-6", 1], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-27", 0]}}, {"patchline": {"destination": ["obj-13", 1], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-30", 0], "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-27", 0], "order": 1, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-6", 0], "order": 0, "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-4", 1], "source": ["obj-32", 1]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-38", 0], "order": 0, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 1, "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-37", 0]}}, {"patchline": {"destination": ["obj-41", 1], "source": ["obj-39", 1]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-26", 1], "source": ["obj-41", 1]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-40", 1], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-44", 0], "source": ["obj-45", 0]}}, {"patchline": {"destination": ["obj-45", 0], "source": ["obj-46", 0]}}, {"patchline": {"destination": ["obj-28", 0], "order": 0, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-37", 0], "order": 1, "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-8", 0]}}, {"patchline": {"destination": ["obj-4", 0], "source": ["obj-9", 0]}}], "originid": "pat-17", "parameters": {"obj-32": ["live.gain~[1]", "live.gain~", 0], "obj-41": ["live.gain~[3]", "live.gain~", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "inherited_shortname": 1}, "dependency_cache": [], "autosave": 0, "toolbaradditions": ["packagemanager"]}}