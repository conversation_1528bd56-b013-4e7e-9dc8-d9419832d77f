{"patcher": {"fileversion": 1, "appversion": {"major": 8, "minor": 5, "revision": 5, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [248.0, 176.0, 1269.0, 704.0], "bglocked": 0, "openinpresentation": 0, "default_fontsize": 12.0, "default_fontface": 0, "default_fontname": "<PERSON><PERSON>", "gridonopen": 1, "gridsize": [15.0, 15.0], "gridsnaponopen": 1, "objectsnaponopen": 1, "statusbarvisible": 2, "toolbarvisible": 1, "lefttoolbarpinned": 0, "toptoolbarpinned": 0, "righttoolbarpinned": 0, "bottomtoolbarpinned": 0, "toolbars_unpinned_last_save": 0, "tallnewobj": 0, "boxanimatetime": 200, "enablehscroll": 1, "enablevscroll": 1, "devicewidth": 0.0, "description": "", "digest": "", "tags": "", "style": "", "subpatcher_template": "", "assistshowspatchername": 0, "boxes": [{"box": {"id": "obj-5", "linecount": 6, "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [173.82113820314407, 980.697655081749, 150.0, 102.0], "text": "pack 三个控制参数：\n\nPitch\nStart Stop position\nDuration\n\n"}}, {"box": {"id": "obj-7", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [611.0, 352.8604600429535, 108.0, 22.0], "text": "r SynthgrainDur-<PERSON>"}}, {"box": {"id": "obj-6", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [410.25581669807434, 148.59709465503693, 114.0, 22.0], "text": "r SynthgrainStart-Hi"}}, {"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [258.46511948108673, 690.2403036952019, 100.0, 22.0], "text": "r <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [532.0, 309.09302961826324, 110.0, 22.0], "text": "r Synthgrain<PERSON>ur-Lo"}}, {"box": {"id": "obj-3", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 0, "numoutlets": 1, "outlettype": [""], "patching_rect": [180.5581419467926, 142.09302961826324, 116.0, 22.0], "text": "r SynthgrainStart-Lo"}}, {"box": {"id": "obj-34", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [966.302328824997, 980.697655081749, 59.0, 22.0], "text": "mute 0, 1"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [888.302328824997, 890.697655081749, 58.0, 22.0], "text": "loadbang"}}, {"box": {"id": "obj-31", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [888.302328824997, 980.697655081749, 59.0, 22.0], "text": "mute 1, 0"}}, {"box": {"id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["int", "int", "int"], "patching_rect": [888.302328824997, 1046.697655081749, 56.0, 22.0], "text": "thispoly~"}}, {"box": {"id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 5, "outlettype": ["bang", "bang", "bang", "bang", "bang"], "patching_rect": [64.80000000000001, 420.8837286233902, 62.0, 22.0], "text": "t b b b b b"}}, {"box": {"id": "obj-24", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["int", "int"], "patching_rect": [64.80000000000001, 380.8837286233902, 55.0, 22.0], "text": "stripnote"}}, {"box": {"id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["int", "int"], "patching_rect": [64.80000000000001, 343.8837286233902, 47.0, 22.0], "text": "unpack"}}, {"box": {"id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [156.05, 1394.0, 42.0, 22.0], "saved_object_attributes": {"attr_comment": ""}, "text": "out~ 2"}}, {"box": {"id": "obj-14", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [67.80000000000001, 1394.0, 42.0, 22.0], "saved_object_attributes": {"attr_comment": ""}, "text": "out~ 1"}}, {"box": {"id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [72.80000000000001, 302.8837286233902, 28.0, 22.0], "saved_object_attributes": {"attr_comment": ""}, "text": "in 1"}}, {"box": {"id": "obj-70", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [480.0, 562.0, 50.0, 22.0]}}, {"box": {"id": "obj-71", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [589.0, 480.0, 81.5, 22.0], "text": "-"}}, {"box": {"id": "obj-72", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [480.0, 520.0, 71.5, 22.0], "text": "+"}}, {"box": {"id": "obj-73", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [480.0, 472.0, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-74", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [611.0, 428.0, 50.0, 22.0]}}, {"box": {"id": "obj-75", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [532.0, 428.0, 50.0, 22.0]}}, {"box": {"id": "obj-69", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [296.6046540737152, 480.0, 50.0, 22.0]}}, {"box": {"id": "obj-67", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [410.25581669807434, 309.09302961826324, 81.5, 22.0], "text": "-"}}, {"box": {"id": "obj-66", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [296.6046540737152, 409.09302961826324, 71.5, 22.0], "text": "+"}}, {"box": {"id": "obj-65", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [296.6046540737152, 361.09302961826324, 49.0, 22.0], "text": "random"}}, {"box": {"id": "obj-64", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [410.25581669807434, 185.69012069702148, 50.0, 22.0]}}, {"box": {"id": "obj-62", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [180.5581419467926, 175.69768369197845, 50.0, 22.0]}}, {"box": {"floatoutput": 1, "id": "obj-57", "maxclass": "dial", "numinlets": 1, "numoutlets": 1, "outlettype": ["float"], "parameter_enable": 0, "patching_rect": [470.0, 818.0, 40.0, 40.0]}}, {"box": {"id": "obj-56", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [444.0, 749.0, 76.0, 22.0], "text": "random 128."}}, {"box": {"id": "obj-48", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [67.80000000000001, 1313.0, 50.5, 22.0], "text": "pan2"}}, {"box": {"id": "obj-42", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [92.0, 935.0, 50.0, 22.0]}}, {"box": {"id": "obj-40", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [92.0, 827.0, 29.5, 22.0], "text": "+"}}, {"box": {"id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [182.45349144935602, 784.7441868782043, 29.5, 22.0], "text": "* 1."}}, {"box": {"id": "obj-38", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [67.80000000000001, 1101.0, 59.0, 22.0], "text": "$1, $2 $3"}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": [""], "patching_rect": [68.0, 1052.0, 67.0, 22.0], "text": "pack 0 0 0."}}, {"box": {"format": 6, "id": "obj-35", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [258.46511948108673, 733.2325588464737, 50.0, 22.0]}}, {"box": {"id": "obj-33", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [156.05, 733.2325588464737, 50.0, 22.0]}}, {"box": {"id": "obj-29", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [75.58139979839325, 733.2325588464737, 50.0, 22.0]}}, {"box": {"id": "obj-21", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [67.80000000000001, 1165.6, 34.0, 22.0], "text": "line~"}}, {"box": {"id": "obj-22", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [156.05, 1083.5581401586533, 72.0, 22.0], "text": "0, 1000 512"}}, {"box": {"id": "obj-20", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [67.80000000000001, 1203.658725619316, 134.0, 22.0], "text": "play~ <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-18", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [67.80000000000001, 1271.4000036716461, 29.5, 22.0], "text": "*~"}}, {"box": {"id": "obj-17", "maxclass": "scope~", "numinlets": 2, "numoutlets": 0, "patching_rect": [736.0, 1280.4000036716461, 130.0, 130.0]}}, {"box": {"id": "obj-16", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [736.0000022530555, 1153.4000040888786, 34.0, 22.0], "text": "line~"}}, {"box": {"id": "obj-15", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [736.0000022530555, 1113.4000066161157, 45.0, 22.0], "text": "0, 1 $1"}}, {"box": {"id": "obj-13", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [736.0000022530555, 1199.2000047326087, 138.0, 22.0], "text": "wave~ SynthgrainEnv 0."}}], "lines": [{"patchline": {"destination": ["obj-35", 0], "source": ["obj-1", 0]}}, {"patchline": {"destination": ["obj-23", 0], "source": ["obj-10", 0]}}, {"patchline": {"destination": ["obj-17", 0], "order": 0, "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-18", 1], "midpoints": [745.5000022530555, 1236.900002157688, 87.80000000000001, 1236.900002157688], "order": 1, "source": ["obj-13", 0]}}, {"patchline": {"destination": ["obj-16", 0], "source": ["obj-15", 0]}}, {"patchline": {"destination": ["obj-13", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-16", 1]}}, {"patchline": {"destination": ["obj-48", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-75", 0], "source": ["obj-2", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-20", 0]}}, {"patchline": {"destination": ["obj-20", 0], "source": ["obj-21", 0]}}, {"patchline": {"destination": ["obj-24", 1], "source": ["obj-23", 1]}}, {"patchline": {"destination": ["obj-24", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-24", 0]}}, {"patchline": {"destination": ["obj-34", 0], "source": ["obj-26", 3]}}, {"patchline": {"destination": ["obj-56", 0], "source": ["obj-26", 2]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-26", 1]}}, {"patchline": {"destination": ["obj-36", 0], "order": 1, "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-40", 0], "order": 0, "source": ["obj-29", 0]}}, {"patchline": {"destination": ["obj-62", 0], "source": ["obj-3", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-31", 0]}}, {"patchline": {"destination": ["obj-31", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-15", 0], "midpoints": [165.55, 943.7000033080578, 745.5000022530555, 943.7000033080578], "order": 0, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-36", 2], "midpoints": [165.55, 989.1627879738808, 125.5, 989.1627879738808], "order": 2, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-39", 0], "order": 1, "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-34", 0]}}, {"patchline": {"destination": ["obj-39", 1], "source": ["obj-35", 0]}}, {"patchline": {"destination": ["obj-38", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-21", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-40", 1], "midpoints": [191.95349144935602, 816.3720934391022, 112.0, 816.3720934391022], "source": ["obj-39", 0]}}, {"patchline": {"destination": ["obj-42", 0], "midpoints": [101.5, 891.5, 101.5, 891.5], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-36", 1], "source": ["obj-42", 0]}}, {"patchline": {"destination": ["obj-14", 0], "source": ["obj-48", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-48", 1]}}, {"patchline": {"destination": ["obj-57", 0], "midpoints": [453.5, 794.0, 479.5, 794.0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-48", 2], "midpoints": [479.5, 1242.0, 98.30000000000001, 1242.0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-64", 0], "source": ["obj-6", 0]}}, {"patchline": {"destination": ["obj-66", 1], "midpoints": [190.0581419467926, 313.59302961826324, 358.6046540737152, 313.59302961826324], "order": 1, "source": ["obj-62", 0]}}, {"patchline": {"destination": ["obj-67", 1], "order": 0, "source": ["obj-62", 0]}}, {"patchline": {"destination": ["obj-67", 0], "source": ["obj-64", 0]}}, {"patchline": {"destination": ["obj-66", 0], "source": ["obj-65", 0]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-66", 0]}}, {"patchline": {"destination": ["obj-65", 1], "source": ["obj-67", 0]}}, {"patchline": {"destination": ["obj-29", 0], "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-33", 0], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-73", 1], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-70", 0], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-72", 0], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-74", 0]}}, {"patchline": {"destination": ["obj-71", 1], "order": 0, "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-72", 1], "midpoints": [541.5, 484.5, 542.0, 484.5], "order": 1, "source": ["obj-75", 0]}}]}}