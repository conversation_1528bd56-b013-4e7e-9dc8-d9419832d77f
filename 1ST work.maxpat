{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [34.0, 77.0, 1580.0, 993.0], "gridsize": [15.0, 15.0], "boxes": [{"box": {"channels": 1, "fontsize": 13.0, "id": "obj-110", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 1, "numoutlets": 4, "orientation": 1, "outlettype": ["signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [43.85964721441269, 1002.339137673378, 136.0, 35.0], "saved_attribute_attributes": {"valueof": {"parameter_initial": [-70], "parameter_initial_enable": 1, "parameter_longname": "live.gain~[1]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "showname": 0, "varname": "live.gain~[1]"}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-109", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.Oscillator.maxpat", "numinlets": 6, "numoutlets": 2, "offset": [0.0, 0.0], "outlettype": ["signal", "signal"], "patching_rect": [36.25730836391449, 860.818675994873, 314.0, 116.0], "varname": "bp.Oscillator", "viewvisibility": 1}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-107", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.MI<PERSON>.maxpat", "numinlets": 0, "numoutlets": 5, "offset": [0.0, 0.0], "outlettype": ["signal", "signal", "signal", "signal", "signal"], "patching_rect": [36.25730836391449, 718.1286237239838, 185.0, 116.0], "varname": "bp.<PERSON>", "viewvisibility": 1}}, {"box": {"id": "obj-92", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [496.2105052471161, 1074.1227613687515, 31.578945994377136, 20.0], "text": "max"}}, {"box": {"id": "obj-90", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [397.41518729925156, 890.8888502120972, 44.44444251060486, 20.0], "text": "on/off"}}, {"box": {"id": "obj-88", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [586.5496820807457, 707.239734351635, 50.0, 20.0], "text": "bpm"}}, {"box": {"id": "obj-86", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [568.7295078635216, 792.9824216365814, 29.5, 22.0], "text": "- 0"}}, {"box": {"id": "obj-81", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [530.4093336462975, 760.8186803460121, 24.0, 24.0]}}, {"box": {"id": "obj-79", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [443.8596298098564, 1099.9999521374702, 24.0, 24.0]}}, {"box": {"id": "obj-75", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [469.8596298098564, 728.0701437592506, 24.0, 24.0]}}, {"box": {"id": "obj-73", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [469.8596298098564, 889.8888502120972, 50.0, 22.0]}}, {"box": {"id": "obj-71", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [469.8596298098564, 840.3508406281471, 29.5, 22.0], "text": "/"}}, {"box": {"id": "obj-70", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [586.5496820807457, 729.239734351635, 50.0, 22.0]}}, {"box": {"id": "obj-68", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [469.8596298098564, 785.3800827860832, 42.0, 22.0], "text": "60000"}}, {"box": {"id": "obj-65", "maxclass": "led", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [499.99997824430466, 1048.1227613687515, 24.0, 24.0]}}, {"box": {"channels": 1, "fontsize": 13.0, "id": "obj-53", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 1, "numoutlets": 4, "orientation": 1, "outlettype": ["signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [558.4795078635216, 1025.7309495210648, 136.0, 35.0], "saved_attribute_attributes": {"valueof": {"parameter_initial": [-70], "parameter_initial_enable": 1, "parameter_longname": "live.gain~", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~", "parameter_type": 0, "parameter_unitstyle": 4}}, "showname": 0, "varname": "live.gain~"}}, {"box": {"format": 6, "id": "obj-52", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [558.4795078635216, 889.8888502120972, 50.0, 22.0]}}, {"box": {"id": "obj-44", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [43.85964721441269, 1068.421006143093, 45.0, 45.0]}}, {"box": {"id": "obj-41", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [558.4795078635216, 945.6139939427376, 43.0, 22.0], "text": "cycle~"}}, {"box": {"id": "obj-40", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [443.8596298098564, 1049.1227613687515, 50.0, 22.0]}}, {"box": {"id": "obj-38", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [443.8596298098564, 888.8888502120972, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [443.8596298098564, 941.5204268693924, 45.0, 22.0], "text": "metro i"}}, {"box": {"id": "obj-32", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [443.8596298098564, 988.3040505647659, 69.0, 22.0], "text": "counter 1 8"}}, {"box": {"id": "obj-29", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [258.3947256207466, 1057.894690811634, 39.0, 22.0], "text": "metro"}}, {"box": {"id": "obj-28", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [257.8947256207466, 988.3040505647659, 40.0, 22.0], "text": "select"}}, {"box": {"id": "obj-27", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [255.0, 433.91810977458954, 24.0, 24.0], "svg": ""}}, {"box": {"format": 6, "id": "obj-23", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [242.0, 576.6081620454788, 50.0, 22.0]}}, {"box": {"id": "obj-21", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [254.3859538435936, 606.4327221512794, 50.0, 22.0]}}], "lines": [{"patchline": {"destination": ["obj-109", 0], "source": ["obj-107", 0]}}, {"patchline": {"destination": ["obj-110", 0], "source": ["obj-109", 0]}}, {"patchline": {"destination": ["obj-44", 1], "order": 0, "source": ["obj-110", 0]}}, {"patchline": {"destination": ["obj-44", 0], "order": 1, "source": ["obj-110", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-32", 2]}}, {"patchline": {"destination": ["obj-32", 0], "source": ["obj-36", 0]}}, {"patchline": {"destination": ["obj-36", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-79", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-52", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-81", 0], "order": 1, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-86", 0], "order": 0, "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-36", 1], "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-68", 0], "source": ["obj-75", 0]}}, {"patchline": {"destination": ["obj-71", 0], "source": ["obj-81", 0]}}, {"patchline": {"destination": ["obj-71", 1], "source": ["obj-86", 0]}}], "originid": "pat-11", "parameters": {"obj-107::obj-129": ["BendRange", "BendRange", 0], "obj-107::obj-15::obj-2": ["pastebang", "pastebang", 0], "obj-107::obj-20": ["ModWheelActivityLED", "ModWheelActivityLED", 0], "obj-107::obj-69": ["GateLED", "GateLED", 0], "obj-107::obj-9": ["PitchActivityLED", "PitchActivityLED", 0], "obj-109::obj-106": ["CV3", "CV3", 0], "obj-109::obj-107": ["Linear", "Linear", 0], "obj-109::obj-11": ["PWM", "PWM", 0], "obj-109::obj-129": ["CV2", "CV2", 0], "obj-109::obj-36": ["PW", "PW", 0], "obj-109::obj-4": ["Waveform", "Waveform", 0], "obj-109::obj-45": ["FreqMode", "FreqMode", 0], "obj-109::obj-46": ["Offset", "Offset", 0], "obj-109::obj-51": ["Freq", "Freq", 0], "obj-109::obj-53": ["Mute", "Mute", 0], "obj-110": ["live.gain~[1]", "live.gain~", 0], "obj-53": ["live.gain~", "live.gain~", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "inherited_shortname": 1}, "dependency_cache": [{"name": "bp.MI<PERSON>.maxpat", "bootpath": "C74:/packages/Beap/clippings/BEAP/Input", "type": "JSON", "implicit": 1}, {"name": "bp.Oscillator.maxpat", "bootpath": "C74:/packages/Beap/clippings/BEAP/Oscillator", "type": "JSON", "implicit": 1}, {"name": "pastebang.maxpat", "bootpath": "C74:/packages/Beap/misc", "type": "JSON", "implicit": 1}, {"name": "sine.svg", "bootpath": "C74:/media/max/picts/m4l-picts", "type": "svg", "implicit": 1}, {"name": "square.svg", "bootpath": "C74:/media/max/picts/m4l-picts", "type": "svg", "implicit": 1}, {"name": "up.svg", "bootpath": "C74:/media/max/picts/m4l-picts", "type": "svg", "implicit": 1}, {"name": "updown.svg", "bootpath": "C74:/media/max/picts/m4l-picts", "type": "svg", "implicit": 1}], "autosave": 0, "toolbaradditions": ["BEAP", "packagemanager"]}}