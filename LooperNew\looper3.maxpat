{"patcher": {"fileversion": 1, "appversion": {"major": 9, "minor": 0, "revision": 2, "architecture": "x64", "modernui": 1}, "classnamespace": "box", "rect": [35.0, 78.0, 1980.0, 993.0], "openinpresentation": 1, "gridsize": [15.0, 15.0], "style": "rnbomonokai", "boxes": [{"box": {"id": "obj-36", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 10, "outlettype": ["float", "list", "float", "float", "float", "float", "float", "", "int", ""], "patching_rect": [2587.4956636428833, 1276.190336227417, 158.8652515411377, 23.0], "text": "info~ Synth<PERSON>in-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-216", "maxclass": "ezadc~", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [100.0, 100.0, 45.0, 45.0], "presentation": 1, "presentation_rect": [5.0, 5.0, 45.0, 45.0]}}, {"box": {"id": "obj-1", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [100.0, 300.0, 146.0, 23.0], "text": "record~ <PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-23", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [100.0, 200.0, 34.0, 23.0], "text": "*~ 0."}}, {"box": {"id": "obj-26", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "bang"], "patching_rect": [150.0, 150.0, 34.0, 23.0], "text": "line~"}}, {"box": {"id": "obj-28", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [200.0, 100.0, 99.0, 23.0], "text": "0, 1 100 0 10000"}}, {"box": {"id": "obj-30", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [250.0, 50.0, 24.0, 24.0], "presentation": 1, "presentation_rect": [55.0, 5.0, 24.0, 24.0]}}, {"box": {"id": "obj-2", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 10, "outlettype": ["float", "list", "float", "float", "float", "float", "float", "", "int", ""], "patching_rect": [734.0, 596.0, 138.0, 23.0], "text": "info~ Synth<PERSON>in-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-19", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [2700.709276199341, 441.1347609758377, 94.0, 23.0], "text": "prepend replace"}}, {"box": {"decodemode": 1, "id": "obj-16", "maxclass": "live.drop", "numinlets": 1, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 1, "patching_rect": [2700.709276199341, 355.3191563487053, 140.0, 60.0], "presentation": 1, "presentation_rect": [310.8107900619507, 279.7297110557556, 224.32430934906006, 105.40539836883545], "saved_attribute_attributes": {"valueof": {"parameter_invisible": 1, "parameter_longname": "live.drop", "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.drop", "parameter_type": 4}}, "varname": "live.drop"}}, {"box": {"id": "obj-7", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [1307.0, 82.02247846126556, 46.06741940975189, 46.06741940975189], "presentation": 1, "presentation_rect": [0.765115946531296, 82.02247846126556, 31.81915631890297, 31.81915631890297], "svg": ""}}, {"box": {"format": 6, "id": "obj-155", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1906.5217027664185, 632.6086835861206, 50.0, 23.0], "presentation": 1, "presentation_rect": [141.66666531562805, 394.6428533792496, 50.0, 23.0]}}, {"box": {"id": "obj-149", "maxclass": "preset", "numinlets": 1, "numoutlets": 5, "outlettype": ["preset", "int", "preset", "int", ""], "patching_rect": [1872.06, 683.08, 100.0, 40.0], "presentation": 1, "presentation_rect": [35.23809975385666, 394.6428533792496, 100.0, 40.0], "preset_data": [{"number": 1, "data": [5, "obj-135", "number", "float", 0.0, 5, "obj-136", "number", "float", 0.0, 5, "obj-87", "number", "float", 6.900000095367432, 5, "obj-84", "number", "float", 3.900000095367432, 5, "obj-85", "number", "float", 3.400000095367432, 5, "obj-83", "number", "float", 1.919999957084656, 5, "obj-82", "number", "float", 5.400000095367432, 5, "obj-86", "number", "float", 1.200000047683716, 196, "obj-80", "matrixctrl", "list", 0, 0, 0.091373620033265, 0, 1, 0.0, 0, 2, 0.0, 0, 3, 0.0, 0, 4, 0.0, 0, 5, 0.0, 0, 6, 0.0, 0, 7, 0.0, 1, 0, 0.213559303283692, 1, 1, 0.0, 1, 2, 0.0, 1, 3, 0.0, 1, 4, 0.290566014528275, 1, 5, 0.0, 1, 6, 0.0, 1, 7, 0.0, 2, 0, 0.159322013854981, 2, 1, 0.0, 2, 2, 0.27368420791626, 2, 3, 0.105660368919373, 2, 4, 0.151111070632935, 2, 5, 0.0, 2, 6, 0.0, 2, 7, 0.0, 3, 0, 0.154161341190338, 3, 1, 0.0, 3, 2, 0.272982435226441, 3, 3, 0.309433937549591, 3, 4, 0.0, 3, 5, 0.0, 3, 6, 0.0, 3, 7, 0.0, 4, 0, 0.128813538551331, 4, 1, 0.392452798843384, 4, 2, 0.140584756851198, 4, 3, 0.0, 4, 4, 0.0, 4, 5, 0.0, 4, 6, 0.0, 4, 7, 0.0, 5, 0, 0.271186423301698, 5, 1, 0.0, 5, 2, 0.0, 5, 3, 0.0, 5, 4, 0.0, 5, 5, 0.0, 5, 6, 0.0, 5, 7, 0.0, 6, 0, 0.0, 6, 1, 0.286792429924011, 6, 2, 0.167017513275148, 6, 3, 0.199999963760377, 6, 4, 0.0, 6, 5, 0.0, 6, 6, 0.0, 6, 7, 0.0, 7, 0, 0.0, 7, 1, 0.0, 7, 2, 0.0, 7, 3, 0.0, 7, 4, 0.0, 7, 5, 0.0, 7, 6, 0.0, 7, 7, 0.0]}, {"number": 2, "data": [5, "obj-135", "number", "float", 0.259999990463257, 5, "obj-136", "number", "float", 0.013000000268221, 5, "obj-87", "number", "float", 0.560000002384186, 5, "obj-84", "number", "float", 0.129999995231628, 5, "obj-85", "number", "float", 0.170000001788139, 5, "obj-83", "number", "float", 0.579999983310699, 5, "obj-82", "number", "float", 2.089999914169312, 5, "obj-86", "number", "float", 0.883000016212463, 196, "obj-80", "matrixctrl", "list", 0, 0, 0.091373620033265, 0, 1, 0.0, 0, 2, 0.0, 0, 3, 0.0, 0, 4, 0.0, 0, 5, 0.754716920852661, 0, 6, 0.0, 0, 7, 0.0, 1, 0, 0.213559303283692, 1, 1, 0.0, 1, 2, 0.0, 1, 3, 0.0, 1, 4, 0.290566014528275, 1, 5, 0.0, 1, 6, 0.0, 1, 7, 0.483018829345703, 2, 0, 0.487623874425888, 2, 1, 0.0, 2, 2, 0.568023807048798, 2, 3, 0.105660368919373, 2, 4, 0.151111070632935, 2, 5, 0.0, 2, 6, 0.505660336971283, 2, 7, 0.0, 3, 0, 0.154161341190338, 3, 1, 0.0, 3, 2, 0.272982435226441, 3, 3, 0.309433937549591, 3, 4, 0.0, 3, 5, 0.0, 3, 6, 0.0, 3, 7, 0.0, 4, 0, 0.128813538551331, 4, 1, 0.392452798843384, 4, 2, 0.472660202026368, 4, 3, 0.0, 4, 4, 0.0, 4, 5, 0.0, 4, 6, 0.0, 4, 7, 0.743396167039871, 5, 0, 0.271186423301698, 5, 1, 0.0, 5, 2, 0.0, 5, 3, 0.0, 5, 4, 0.0, 5, 5, 0.0, 5, 6, 0.0, 5, 7, 0.0, 6, 0, 0.0, 6, 1, 0.286792429924011, 6, 2, 0.167017513275148, 6, 3, 0.199999963760377, 6, 4, 0.0, 6, 5, 0.0, 6, 6, 0.0, 6, 7, 0.0, 7, 0, 0.0, 7, 1, 0.0, 7, 2, 0.0, 7, 3, 0.0, 7, 4, 0.0, 7, 5, 0.0, 7, 6, 0.0, 7, 7, 0.0]}]}}, {"box": {"id": "obj-141", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [2084.210506439209, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-142", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1972.8069987297058, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-143", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [2014.9122614860535, 1082.456130027771, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-144", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [2014.9122614860535, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"id": "obj-145", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1920.1754202842712, 1082.456130027771, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-146", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1920.1754202842712, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"format": 6, "id": "obj-135", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2007.0712115764618, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [240.4761881828308, 357.36918845772743, 50.0, 23.0]}}, {"box": {"format": 6, "id": "obj-136", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1926.4260467290878, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [184.52380776405334, 357.36918845772743, 50.0, 23.0]}}, {"box": {"id": "obj-137", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [2007.0712115764618, 783.8710023164749, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-140", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1927.5013155937195, 783.8710023164749, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-134", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1860.526298046112, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-133", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1749.122790336609, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-132", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1629.8245458602905, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-131", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1518.4210381507874, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-130", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1402.6315655708313, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-128", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1285.4092381000519, 1143.8596382141113, 103.0, 23.0], "text": "scale~ 0 1 5 4000"}}, {"box": {"id": "obj-113", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1791.2280530929565, 1082.456130027771, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-114", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1791.2280530929565, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"id": "obj-108", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1696.4912118911743, 1082.456130027771, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-110", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1696.4912118911743, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"id": "obj-104", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1601.754370689392, 1082.456130027771, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-105", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1601.754370689392, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"id": "obj-100", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1499.1227927207947, 1082.456130027771, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-101", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1499.1227927207947, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"id": "obj-96", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1402.6315655708313, 1082.456130027771, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-97", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1402.6315655708313, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"id": "obj-95", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1284.5320451259613, 1081.5789370536804, 72.0, 23.0], "text": "pong~ 0 0 1"}}, {"box": {"id": "obj-93", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 6, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1284.5320451259613, 1038.5964813232422, 86.0, 23.0], "text": "scale~ -2 2 0 1"}}, {"box": {"format": 6, "id": "obj-86", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1452.232477426529, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [184.52380776405334, 240.0000307559967, 50.0, 23.0]}}, {"box": {"format": 6, "id": "obj-87", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1864.060452580452, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [240.4761881828308, 319.04761600494385, 50.0, 23.0]}}, {"box": {"format": 6, "id": "obj-84", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1783.415287733078, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [184.52380776405334, 319.04761600494385, 50.0, 23.0]}}, {"box": {"format": 6, "id": "obj-85", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1700.6195851564407, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [240.4761881828308, 278.5714259147644, 50.0, 23.0]}}, {"box": {"format": 6, "id": "obj-83", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1618.8991514444351, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [184.52380776405334, 278.5714259147644, 50.0, 23.0]}}, {"box": {"format": 6, "id": "obj-82", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [1536.1034488677979, 752.688205242157, 50.0, 23.0], "presentation": 1, "presentation_rect": [240.4761881828308, 240.0000307559967, 50.0, 23.0]}}, {"box": {"dialmode": 2, "id": "obj-80", "maxclass": "matrixctrl", "numinlets": 1, "numoutlets": 2, "outlettype": ["list", "list"], "parameter_enable": 0, "patching_rect": [1284.5320451259613, 730.838287577033, 144.85981196165085, 140.36915770173073], "presentation": 1, "presentation_rect": [35.23809975385666, 240.0000307559967, 144.85981196165085, 140.36915770173073], "rows": 8}}, {"box": {"id": "obj-57", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 8, "numoutlets": 9, "outlettype": ["signal", "signal", "signal", "signal", "signal", "signal", "signal", "signal", ""], "patching_rect": [1284.5320451259613, 964.5161715745926, 623.6842045783997, 23.0], "text": "matrix~ 8 8"}}, {"box": {"id": "obj-56", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1452.232477426529, 782.7957334518433, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-55", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1864.060452580452, 782.7957334518433, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-54", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1785.5658254623413, 782.7957334518433, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-53", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1700.6195851564407, 782.7957334518433, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-51", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1618.8991514444351, 782.7957334518433, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-50", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [1536.1034488677979, 782.7957334518433, 42.0, 23.0], "text": "cycle~"}}, {"box": {"id": "obj-38", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2114.285482406616, 1783.3331377506256, 29.0, 37.0], "text": "stop"}}, {"box": {"id": "obj-32", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2023.80930185318, 1730.9521911144257, 72.0, 37.0], "text": "startwindow"}}, {"box": {"id": "obj-10", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1427.0, 246.86000000000013, 90.0, 23.0], "text": "loadmess 2000"}}, {"box": {"id": "obj-5", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [870.0000207424164, 666.0950700044632, 122.0, 23.0], "text": "set Synthgrain-<PERSON><PERSON><PERSON>"}}, {"box": {"format": 6, "id": "obj-17", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2462.1358885765076, 156.3106774687767, 50.0, 23.0]}}, {"box": {"id": "obj-8", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [2453.4, 207.77, 66.0, 23.0], "text": "cycle~ 100"}}, {"box": {"id": "obj-43", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [3349.9996325969696, 1269.0474798679352, 58.0, 23.0], "text": "loadbang"}}, {"box": {"floatoutput": 1, "id": "obj-40", "maxclass": "rslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 0, "patching_rect": [664.2857301235199, 875.3192467689514, 150.51545548439026, 24.74226665496826], "presentation": 1, "presentation_rect": [35.23809975385666, 184.76192843914032, 195.78948068618774, 43.84210515022278], "size": 1445.4421768707482}}, {"box": {"id": "obj-236", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2466.6663961410522, 964.2856085300446, 24.0, 24.0]}}, {"box": {"id": "obj-235", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2402.380688905716, 964.2856085300446, 24.0, 24.0]}}, {"box": {"id": "obj-234", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2340.475933790207, 964.2856085300446, 24.0, 24.0]}}, {"box": {"id": "obj-233", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [2473.809252500534, 926.1903746128082, 61.0, 23.0], "text": "delay 100"}}, {"box": {"id": "obj-232", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [2407.1425931453705, 926.1903746128082, 61.0, 23.0], "text": "delay 100"}}, {"box": {"id": "obj-231", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [2340.475933790207, 926.1903746128082, 61.0, 23.0], "text": "delay 100"}}, {"box": {"id": "obj-230", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2302.3806998729706, 959.52370429039, 24.0, 24.0]}}, {"box": {"id": "obj-228", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 4, "numoutlets": 4, "outlettype": ["bang", "bang", "bang", ""], "patching_rect": [2302.3806998729706, 902.3808534145355, 54.0, 23.0], "text": "sel 1 2 0"}}, {"box": {"id": "obj-227", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2302.3806998729706, 866.6665716171265, 59.0, 23.0], "text": "random 3"}}, {"box": {"id": "obj-226", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [1000.0000238418579, 1315.7143170833588, 70.0, 23.0], "text": "loadmess 1"}}, {"box": {"id": "obj-223", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 3, "outlettype": ["bang", "bang", ""], "patching_rect": [574.2857279777527, 794.2857332229614, 44.0, 23.0], "text": "sel 0 1"}}, {"box": {"id": "obj-222", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["int"], "patching_rect": [574.2857279777527, 767.1428754329681, 29.5, 23.0], "text": "> 0"}}, {"box": {"id": "obj-221", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [595.4248554110527, 839.86930757761, 50.0, 23.0], "text": "4000."}}, {"box": {"id": "obj-218", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [500.00001192092896, 760.784337759018, 29.5, 23.0], "text": "t f f"}}, {"box": {"id": "obj-215", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [2036.5385295152664, 41.34615522623062, 70.0, 23.0], "text": "loadmess 1"}}, {"box": {"id": "obj-211", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [552.9411939382553, 845.0980659127235, 29.5, 23.0], "text": "0"}}, {"box": {"fontname": "<PERSON><PERSON>", "fontsize": 13.0, "id": "obj-208", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["signal"], "patching_rect": [500.00001192092896, 828.5714483261108, 35.0, 23.0], "text": "sig~"}}, {"box": {"id": "obj-198", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [837.1428771018982, 1240.0000295639038, 58.0, 23.0], "text": "loadbang"}}, {"box": {"id": "obj-193", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [500.00001192092896, 612.8571574687958, 76.23791253566742, 76.23791253566742]}}, {"box": {"id": "obj-183", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [2164.285476922989, 876.1903800964355, 34.0, 23.0], "text": "sel 2"}}, {"box": {"direction": 0, "id": "obj-182", "maxclass": "live.grid", "numinlets": 2, "numoutlets": 6, "outlettype": ["", "", "", "", "", ""], "parameter_enable": 1, "patching_rect": [2164.285476922989, 673.764935092926, 484.9314715862274, 49.315064907073975], "presentation": 1, "presentation_rect": [388.5714783668518, 7.619048595428467, 331.5217328071594, 42.39130353927612], "rows": 2, "saved_attribute_attributes": {"valueof": {"parameter_invisible": 1, "parameter_longname": "live.grid[1]", "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.grid", "parameter_type": 3}}, "varname": "live.grid[1]"}}, {"box": {"id": "obj-181", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["bang", ""], "patching_rect": [500.00001192092896, 561.4285848140717, 34.0, 23.0], "text": "sel 2"}}, {"box": {"id": "obj-172", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1307.0, 324.8600000000001, 56.0, 37.0], "text": "metro 2n"}}, {"box": {"direction": 0, "id": "obj-171", "maxclass": "live.grid", "numinlets": 2, "numoutlets": 6, "outlettype": ["", "", "", "", "", ""], "parameter_enable": 1, "patching_rect": [1307.0, 440.14282989501953, 301.0, 56.0], "presentation": 1, "presentation_rect": [35.23809975385666, 5.71428644657135, 332.2380946278572, 42.857148349285126], "rows": 2, "saved_attribute_attributes": {"valueof": {"parameter_invisible": 1, "parameter_longname": "live.grid", "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.grid", "parameter_type": 3}}, "varname": "live.grid"}}, {"box": {"id": "obj-170", "linecount": 2, "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 5, "numoutlets": 4, "outlettype": ["int", "", "", "int"], "patching_rect": [1307.0, 380.8600000000001, 73.0, 37.0], "text": "counter 1 16"}}, {"box": {"id": "obj-169", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [500.00001192092896, 717.1428742408752, 126.0, 23.0], "text": "random @range -2. 2."}}, {"box": {"id": "obj-153", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [863.8554536104202, 833.4578335285187, 97.0, 23.0], "text": "loadmess 10000"}}, {"box": {"id": "obj-152", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [664.2857301235199, 741.0, 70.0, 23.0], "text": "loadmess 0"}}, {"box": {"format": 6, "id": "obj-151", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [655.714298248291, 951.8072640895844, 61.68627846240997, 23.0]}}, {"box": {"format": 6, "id": "obj-150", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [590.714298248291, 918.571450471878, 50.0, 23.0]}}, {"box": {"id": "obj-139", "maxclass": "preset", "numinlets": 1, "numoutlets": 5, "outlettype": ["preset", "int", "preset", "int", ""], "patching_rect": [1000.0000238418579, 1351.4286036491394, 100.0, 40.0], "preset_data": [{"number": 1, "data": [5, "obj-41", "toggle", "int", 1, 5, "obj-533", "live.gain~", "float", -0.066532336175442, 5, "obj-515", "number", "int", 1015, 5, "obj-513", "number", "int", 442, 5, "obj-507", "number", "float", 1.455686569213867, 5, "obj-491", "number", "int", 226, 5, "obj-490", "number", "int", 3472, 6, "obj-483", "rslider", "list", 1015, 226, 6, "obj-481", "rslider", "list", 442, 3472, 5, "obj-476", "number", "int", 11500, 5, "obj-73", "live.gain~", "float", 0.102631650865078, 5, "obj-150", "number", "float", 0.0, 5, "obj-151", "number", "float", 4000.0, 5, "obj-171", "live.grid", "mode", 0, 5, "obj-171", "live.grid", "matrixmode", 0, 5, "obj-171", "live.grid", "columns", 16, 5, "obj-171", "live.grid", "rows", 2, 7, "obj-171", "live.grid", "constraint", 1, 1, 1, 7, "obj-171", "live.grid", "constraint", 2, 1, 1, 7, "obj-171", "live.grid", "constraint", 3, 1, 1, 7, "obj-171", "live.grid", "constraint", 4, 1, 1, 7, "obj-171", "live.grid", "constraint", 5, 1, 1, 7, "obj-171", "live.grid", "constraint", 6, 1, 1, 7, "obj-171", "live.grid", "constraint", 7, 1, 1, 7, "obj-171", "live.grid", "constraint", 8, 1, 1, 7, "obj-171", "live.grid", "constraint", 9, 1, 1, 7, "obj-171", "live.grid", "constraint", 10, 1, 1, 7, "obj-171", "live.grid", "constraint", 11, 1, 1, 7, "obj-171", "live.grid", "constraint", 12, 1, 1, 7, "obj-171", "live.grid", "constraint", 13, 1, 1, 7, "obj-171", "live.grid", "constraint", 14, 1, 1, 7, "obj-171", "live.grid", "constraint", 15, 1, 1, 7, "obj-171", "live.grid", "constraint", 16, 1, 1, 20, "obj-171", "live.grid", "steps", 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 20, "obj-171", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, "obj-182", "live.grid", "mode", 0, 5, "obj-182", "live.grid", "matrixmode", 0, 5, "obj-182", "live.grid", "columns", 16, 5, "obj-182", "live.grid", "rows", 2, 7, "obj-182", "live.grid", "constraint", 1, 1, 1, 7, "obj-182", "live.grid", "constraint", 2, 1, 1, 7, "obj-182", "live.grid", "constraint", 3, 1, 1, 7, "obj-182", "live.grid", "constraint", 4, 1, 1, 7, "obj-182", "live.grid", "constraint", 5, 1, 1, 7, "obj-182", "live.grid", "constraint", 6, 1, 1, 7, "obj-182", "live.grid", "constraint", 7, 1, 1, 7, "obj-182", "live.grid", "constraint", 8, 1, 1, 7, "obj-182", "live.grid", "constraint", 9, 1, 1, 7, "obj-182", "live.grid", "constraint", 10, 1, 1, 7, "obj-182", "live.grid", "constraint", 11, 1, 1, 7, "obj-182", "live.grid", "constraint", 12, 1, 1, 7, "obj-182", "live.grid", "constraint", 13, 1, 1, 7, "obj-182", "live.grid", "constraint", 14, 1, 1, 7, "obj-182", "live.grid", "constraint", 15, 1, 1, 7, "obj-182", "live.grid", "constraint", 16, 1, 1, 20, "obj-182", "live.grid", "steps", 1, 2, 1, 2, 1, 1, 1, 1, 1, 2, 2, 1, 2, 2, 2, 1, 20, "obj-182", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"number": 2, "data": [5, "obj-41", "toggle", "int", 1, 5, "obj-533", "live.gain~", "float", -0.066532336175442, 5, "obj-515", "number", "int", 4826, 5, "obj-513", "number", "int", 4866, 5, "obj-507", "number", "float", 2.044173240661621, 5, "obj-491", "number", "int", 7709, 5, "obj-490", "number", "int", 4970, 6, "obj-483", "rslider", "list", 4826, 7709, 6, "obj-481", "rslider", "list", 4866, 4970, 5, "obj-476", "number", "int", 11500, 5, "obj-73", "live.gain~", "float", 0.102631650865078, 5, "obj-150", "number", "float", 0.0, 5, "obj-151", "number", "float", 4000.0, 5, "obj-171", "live.grid", "mode", 0, 5, "obj-171", "live.grid", "matrixmode", 0, 5, "obj-171", "live.grid", "columns", 16, 5, "obj-171", "live.grid", "rows", 2, 7, "obj-171", "live.grid", "constraint", 1, 1, 1, 7, "obj-171", "live.grid", "constraint", 2, 1, 1, 7, "obj-171", "live.grid", "constraint", 3, 1, 1, 7, "obj-171", "live.grid", "constraint", 4, 1, 1, 7, "obj-171", "live.grid", "constraint", 5, 1, 1, 7, "obj-171", "live.grid", "constraint", 6, 1, 1, 7, "obj-171", "live.grid", "constraint", 7, 1, 1, 7, "obj-171", "live.grid", "constraint", 8, 1, 1, 7, "obj-171", "live.grid", "constraint", 9, 1, 1, 7, "obj-171", "live.grid", "constraint", 10, 1, 1, 7, "obj-171", "live.grid", "constraint", 11, 1, 1, 7, "obj-171", "live.grid", "constraint", 12, 1, 1, 7, "obj-171", "live.grid", "constraint", 13, 1, 1, 7, "obj-171", "live.grid", "constraint", 14, 1, 1, 7, "obj-171", "live.grid", "constraint", 15, 1, 1, 7, "obj-171", "live.grid", "constraint", 16, 1, 1, 20, "obj-171", "live.grid", "steps", 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 20, "obj-171", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, "obj-182", "live.grid", "mode", 0, 5, "obj-182", "live.grid", "matrixmode", 0, 5, "obj-182", "live.grid", "columns", 16, 5, "obj-182", "live.grid", "rows", 2, 7, "obj-182", "live.grid", "constraint", 1, 1, 1, 7, "obj-182", "live.grid", "constraint", 2, 1, 1, 7, "obj-182", "live.grid", "constraint", 3, 1, 1, 7, "obj-182", "live.grid", "constraint", 4, 1, 1, 7, "obj-182", "live.grid", "constraint", 5, 1, 1, 7, "obj-182", "live.grid", "constraint", 6, 1, 1, 7, "obj-182", "live.grid", "constraint", 7, 1, 1, 7, "obj-182", "live.grid", "constraint", 8, 1, 1, 7, "obj-182", "live.grid", "constraint", 9, 1, 1, 7, "obj-182", "live.grid", "constraint", 10, 1, 1, 7, "obj-182", "live.grid", "constraint", 11, 1, 1, 7, "obj-182", "live.grid", "constraint", 12, 1, 1, 7, "obj-182", "live.grid", "constraint", 13, 1, 1, 7, "obj-182", "live.grid", "constraint", 14, 1, 1, 7, "obj-182", "live.grid", "constraint", 15, 1, 1, 7, "obj-182", "live.grid", "constraint", 16, 1, 1, 20, "obj-182", "live.grid", "steps", 1, 2, 1, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 1, 20, "obj-182", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}, {"number": 3, "data": [5, "obj-41", "toggle", "int", 1, 5, "obj-533", "live.gain~", "float", -0.066532336175442, 5, "obj-515", "number", "int", 4826, 5, "obj-513", "number", "int", 4866, 5, "obj-507", "number", "float", 2.044173240661621, 5, "obj-491", "number", "int", 7709, 5, "obj-490", "number", "int", 4970, 6, "obj-483", "rslider", "list", 4826, 7709, 6, "obj-481", "rslider", "list", 4866, 4970, 5, "obj-476", "number", "int", 11500, 5, "obj-73", "live.gain~", "float", 0.102631650865078, 5, "obj-150", "number", "float", 0.0, 5, "obj-151", "number", "float", 4000.0, 5, "obj-171", "live.grid", "mode", 0, 5, "obj-171", "live.grid", "matrixmode", 0, 5, "obj-171", "live.grid", "columns", 16, 5, "obj-171", "live.grid", "rows", 2, 7, "obj-171", "live.grid", "constraint", 1, 1, 1, 7, "obj-171", "live.grid", "constraint", 2, 1, 1, 7, "obj-171", "live.grid", "constraint", 3, 1, 1, 7, "obj-171", "live.grid", "constraint", 4, 1, 1, 7, "obj-171", "live.grid", "constraint", 5, 1, 1, 7, "obj-171", "live.grid", "constraint", 6, 1, 1, 7, "obj-171", "live.grid", "constraint", 7, 1, 1, 7, "obj-171", "live.grid", "constraint", 8, 1, 1, 7, "obj-171", "live.grid", "constraint", 9, 1, 1, 7, "obj-171", "live.grid", "constraint", 10, 1, 1, 7, "obj-171", "live.grid", "constraint", 11, 1, 1, 7, "obj-171", "live.grid", "constraint", 12, 1, 1, 7, "obj-171", "live.grid", "constraint", 13, 1, 1, 7, "obj-171", "live.grid", "constraint", 14, 1, 1, 7, "obj-171", "live.grid", "constraint", 15, 1, 1, 7, "obj-171", "live.grid", "constraint", 16, 1, 1, 20, "obj-171", "live.grid", "steps", 2, 1, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 1, 1, 20, "obj-171", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5, "obj-182", "live.grid", "mode", 0, 5, "obj-182", "live.grid", "matrixmode", 0, 5, "obj-182", "live.grid", "columns", 16, 5, "obj-182", "live.grid", "rows", 2, 7, "obj-182", "live.grid", "constraint", 1, 1, 1, 7, "obj-182", "live.grid", "constraint", 2, 1, 1, 7, "obj-182", "live.grid", "constraint", 3, 1, 1, 7, "obj-182", "live.grid", "constraint", 4, 1, 1, 7, "obj-182", "live.grid", "constraint", 5, 1, 1, 7, "obj-182", "live.grid", "constraint", 6, 1, 1, 7, "obj-182", "live.grid", "constraint", 7, 1, 1, 7, "obj-182", "live.grid", "constraint", 8, 1, 1, 7, "obj-182", "live.grid", "constraint", 9, 1, 1, 7, "obj-182", "live.grid", "constraint", 10, 1, 1, 7, "obj-182", "live.grid", "constraint", 11, 1, 1, 7, "obj-182", "live.grid", "constraint", 12, 1, 1, 7, "obj-182", "live.grid", "constraint", 13, 1, 1, 7, "obj-182", "live.grid", "constraint", 14, 1, 1, 7, "obj-182", "live.grid", "constraint", 15, 1, 1, 7, "obj-182", "live.grid", "constraint", 16, 1, 1, 20, "obj-182", "live.grid", "steps", 1, 2, 1, 2, 1, 2, 2, 2, 1, 2, 2, 1, 2, 2, 2, 1, 20, "obj-182", "live.grid", "directions", 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1]}]}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-138", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.Gigaverb.maxpat", "numinlets": 2, "numoutlets": 2, "offset": [0.0, 0.0], "outlettype": ["signal", "signal"], "patching_rect": [638.0, 1710.0, 332.0, 116.0], "presentation": 1, "presentation_rect": [35.23809975385666, 55.238102316856384, 332.0, 116.0], "varname": "bp.<PERSON><PERSON><PERSON><PERSON>[1]", "viewvisibility": 1}}, {"box": {"id": "obj-129", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [525.714298248291, 1014.3388314247131, 149.0, 23.0], "text": "groove~ S<PERSON><PERSON><PERSON>-<PERSON><PERSON>er"}}, {"box": {"id": "obj-117", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 8, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [933.1818462014198, 1060.5263056755066, 233.63635528087616, 23.0], "text": "mc.pack~ 8"}}, {"box": {"id": "obj-116", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [591.4285855293274, 1492.8571784496307, 54.0, 23.0], "text": "cross~ 5"}}, {"box": {"id": "obj-115", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [527.1428697109222, 1492.8571784496307, 54.0, 23.0], "text": "cross~ 5"}}, {"box": {"id": "obj-109", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 3, "outlettype": ["multichannelsignal", "", ""], "patching_rect": [933.1818462014198, 1128.9473576545715, 96.0, 23.0], "text": "mc.m<PERSON><PERSON><PERSON>~"}}, {"box": {"id": "obj-74", "maxclass": "ezdac~", "numinlets": 2, "numoutlets": 0, "patching_rect": [561.4285848140717, 1865.7143301963806, 45.0, 45.0], "presentation": 1, "presentation_rect": [1.904762148857117, 42.857148349285126, 31.428575456142426, 31.428575456142426]}}, {"box": {"id": "obj-73", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "orientation": 1, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [561.4285848140717, 1578.5714662075043, 136.0, 47.0], "presentation": 1, "presentation_rect": [244.7619361281395, 181.90478521585464, 123.0, 47.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[2]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 3, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~[2]", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[2]"}}, {"box": {"id": "obj-72", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [525.714298248291, 1421.4286053180695, 84.0, 23.0], "text": "mc.unpack~ 2"}}, {"box": {"id": "obj-71", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [837.1428771018982, 1312.8571741580963, 104.0, 23.0], "text": "randomrange -1 1"}}, {"box": {"id": "obj-70", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [837.1428771018982, 1345.7143177986145, 108.0, 23.0], "text": "mc.sig~ @chans 8"}}, {"box": {"id": "obj-69", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [525.714298248291, 1385.7143187522888, 126.0, 23.0], "text": "mc.stereo~ @chans 8"}}, {"box": {"id": "obj-68", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1142.8571701049805, 1158.571456193924, 120.0, 23.0], "text": "randomrange 0.2 0.8"}}, {"box": {"id": "obj-66", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [1142.8571701049805, 1210.000028848648, 108.0, 23.0], "text": "mc.sig~ @chans 8"}}, {"box": {"id": "obj-65", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [525.714298248291, 1325.7143173217773, 40.0, 23.0], "text": "mc.*~"}}, {"box": {"id": "obj-25", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": ["multichannelsignal"], "patching_rect": [525.714298248291, 1230.0000293254852, 164.0, 23.0], "text": "mc.delay~ 900000 @chans 8"}}, {"box": {"id": "obj-18", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2164.285476922989, 973.8094170093536, 63.5294144153595, 63.5294144153595]}}, {"box": {"id": "obj-14", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2909.5234904289246, 1297.6189053058624, 150.0, 21.0], "text": "random duration"}}, {"box": {"id": "obj-13", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2850.0, 1038.4760763645172, 150.0, 21.0], "text": "random start stop"}}, {"box": {"bgmode": 0, "border": 0, "clickthrough": 0, "enablehscroll": 0, "enablevscroll": 0, "extract": 1, "id": "obj-11", "lockeddragscroll": 0, "lockedsize": 0, "maxclass": "bpatcher", "name": "bp.Gigaverb.maxpat", "numinlets": 2, "numoutlets": 2, "offset": [0.0, 0.0], "outlettype": ["signal", "signal"], "patching_rect": [2240.4759447574615, 1652.3807711601257, 332.0, 116.0], "presentation": 1, "presentation_rect": [388.5714783668518, 55.238102316856384, 330.5882490873337, 116.47059309482574], "varname": "bp.Gigaverb", "viewvisibility": 1}}, {"box": {"id": "obj-687", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 0, "patching_rect": [2157.142620563507, 1809.5236110687256, 48.25, 23.0], "text": "dac~"}}, {"box": {"id": "obj-442", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2980.9520540237427, 552.3808917999268, 122.0, 23.0], "text": "set Synthgrain-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-450", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [3349.9996325969696, 1319.0474743843079, 106.0, 23.0], "text": "set SynthgrainEnv"}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-455", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [3016.6663358211517, 1142.8570175170898, 125.0, 21.0], "text": "Envolope Generator", "textjustification": 1}}, {"box": {"id": "obj-456", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2705.6738153100014, 807.0922154188156, 131.0, 21.0], "text": "Grain Envolope"}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-459", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2486.0, 1040.4760763645172, 108.39416080713272, 21.0], "presentation": 1, "presentation_rect": [388.5714783668518, 177.14287984371185, 108.39416080713272, 21.0], "text": "Start/Stop Point", "textjustification": 1}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-460", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [2502.0, 1352.0, 56.0, 21.0], "presentation": 1, "presentation_rect": [512.3810180425644, 179.04764199256897, 56.0, 21.0], "text": "Duration", "textjustification": 1}}, {"box": {"buffername": "SynthgrainEnv", "id": "obj-466", "maxclass": "waveform~", "numinlets": 5, "numoutlets": 6, "outlettype": ["float", "float", "float", "float", "list", ""], "patching_rect": [3349.9996325969696, 1369.0474689006805, 256.0, 64.0]}}, {"box": {"buffername": "Synthgrain-<PERSON><PERSON>er", "id": "obj-467", "maxclass": "waveform~", "numinlets": 5, "numoutlets": 6, "outlettype": ["float", "float", "float", "float", "list", ""], "patching_rect": [2980.9520540237427, 614.2856469154358, 256.0, 64.0], "presentation": 1, "presentation_rect": [540.9091283082962, 286.36365616321564, 221.2121365070343, 98.4848552942276], "waveformcolor": [1.0, 1.0, 1.0, 1.0]}}, {"box": {"id": "obj-469", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2587.4017120599747, 448.81892144680023, 35.0, 23.0], "text": "clear"}}, {"box": {"id": "obj-476", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2821.4282619953156, 840.4760982990265, 126.0, 23.0]}}, {"box": {"id": "obj-478", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 10, "outlettype": ["float", "list", "float", "float", "float", "float", "float", "", "int", ""], "patching_rect": [2747.5177878141403, 768.7943422794342, 158.8652515411377, 23.0], "text": "info~ Synth<PERSON>in-<PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-479", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2747.5177878141403, 711.4046838283539, 24.0, 24.0]}}, {"box": {"id": "obj-481", "maxclass": "rslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 0, "patching_rect": [2571.428289413452, 1349.9998519420624, 212.24999797344208, 25.000000000000455], "presentation": 1, "presentation_rect": [512.3810180425644, 202.85716885328293, 116.8421094417572, 25.263158798217773], "size": 1445.0}}, {"box": {"id": "obj-483", "maxclass": "rslider", "numinlets": 2, "numoutlets": 2, "outlettype": ["", ""], "parameter_enable": 0, "patching_rect": [2607.0, 1038.4760763645172, 191.0, 25.0], "presentation": 1, "presentation_rect": [388.5714783668518, 202.85716885328293, 108.39416080713272, 25.263158798217773], "size": 11500.0}}, {"box": {"id": "obj-490", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2790.0, 1395.2379422187805, 50.0, 23.0], "presentation": 1, "presentation_rect": [578.0953121781349, 239.04764968156815, 50.0, 23.0]}}, {"box": {"id": "obj-491", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2796.0, 1088.4760763645172, 50.0, 23.0], "presentation": 1, "presentation_rect": [446.66672390699387, 240.0000307559967, 50.0, 23.0]}}, {"box": {"bgcolor": [1.0, 1.0, 1.0, 1.0], "id": "obj-492", "maxclass": "comment", "numinlets": 1, "numoutlets": 0, "patching_rect": [3009.52347946167, 1338.095091342926, 50.0, 21.0], "presentation": 1, "presentation_rect": [644.0476129055023, 179.04764199256897, 50.0, 21.0], "text": "pitch"}}, {"box": {"id": "obj-494", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2790.0, 1433.3331761360168, 110.0, 23.0], "text": "s Synthgrain<PERSON>ur-<PERSON>"}}, {"box": {"id": "obj-495", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2796.0, 1138.4760763645172, 116.0, 23.0], "text": "s SynthgrainStart-Hi"}}, {"box": {"id": "obj-496", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [3009.52347946167, 1411.9046070575714, 102.0, 23.0], "text": "s <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, {"box": {"id": "obj-497", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2571.428289413452, 1433.3331761360168, 112.0, 23.0], "text": "s SynthgrainDur-Lo"}}, {"box": {"id": "obj-504", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 0, "patching_rect": [2607.0, 1138.4760763645172, 118.0, 23.0], "text": "s SynthgrainStart-Lo"}}, {"box": {"format": 6, "id": "obj-507", "maxclass": "flonum", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [3009.52347946167, 1361.9046125411987, 50.0, 23.0], "presentation": 1, "presentation_rect": [644.0476129055023, 239.04764968156815, 50.0, 23.0]}}, {"box": {"id": "obj-513", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2571.428289413452, 1395.2379422187805, 50.0, 23.0], "presentation": 1, "presentation_rect": [516.1905423402786, 239.04764968156815, 50.0, 23.0]}}, {"box": {"id": "obj-515", "maxclass": "number", "numinlets": 1, "numoutlets": 2, "outlettype": ["", "bang"], "parameter_enable": 0, "patching_rect": [2607.0, 1088.4760763645172, 50.0, 23.0], "presentation": 1, "presentation_rect": [388.5714783668518, 240.0000307559967, 50.0, 23.0]}}, {"box": {"id": "obj-516", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [3157.1425108909607, 1411.9046070575714, 151.0, 23.0], "text": "buffer~ SynthgrainEnv 512"}}, {"box": {"id": "obj-517", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [3157.1425108909607, 1369.0474689006805, 135.0, 23.0], "text": "fill 1 512, apply hanning"}}, {"box": {"id": "obj-518", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2636.11874628067, 452.48227894306183, 48.0, 23.0], "text": "replace"}}, {"box": {"id": "obj-519", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [2587.61874628067, 564.2856523990631, 145.0, 23.0], "text": "buffer~ Synthgrain-Buffer"}}, {"box": {"id": "obj-520", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["float", "bang"], "patching_rect": [3007.1425273418427, 1180.9522514343262, 202.0, 23.0], "text": "buffer~ SynthgrainEnv @samps 512"}}, {"box": {"id": "obj-521", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 1, "outlettype": ["float"], "patching_rect": [2985.713958263397, 842.8570504188538, 123.0, 23.0], "text": "peek~ SynthgrainEnv"}}, {"box": {"id": "obj-522", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2985.713958263397, 799.9999122619629, 57.0, 23.0], "text": "pack 0 0."}}, {"box": {"id": "obj-527", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "patching_rect": [3019.047287940979, 754.7618219852448, 302.0, 23.0], "text": "expr exp(-0.5*pow(($i1-((512-1)/2))/(0.4*((512-1)/2))\\,2))"}}, {"box": {"id": "obj-529", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 3, "outlettype": ["bang", "bang", "int"], "patching_rect": [2957.14253282547, 711.9046838283539, 47.0, 23.0], "text": "uzi 512"}}, {"box": {"id": "obj-531", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [3195.237744808197, 1269.0474798679352, 58.0, 23.0], "text": "loadbang"}}, {"box": {"id": "obj-533", "lastchannelcount": 0, "maxclass": "live.gain~", "numinlets": 2, "numoutlets": 5, "outlettype": ["signal", "signal", "", "float", "list"], "parameter_enable": 1, "patching_rect": [2164.285476922989, 1476.1903142929077, 47.0, 136.0], "presentation": 1, "presentation_rect": [722.6190407276154, 107.04764199256897, 51.0, 165.0], "saved_attribute_attributes": {"valueof": {"parameter_longname": "live.gain~[3]", "parameter_mmax": 6.0, "parameter_mmin": -70.0, "parameter_modmode": 0, "parameter_osc_name": "<default>", "parameter_shortname": "live.gain~[16]", "parameter_type": 0, "parameter_unitstyle": 4}}, "varname": "live.gain~[1]"}}, {"box": {"id": "obj-41", "maxclass": "toggle", "numinlets": 1, "numoutlets": 1, "outlettype": ["int"], "parameter_enable": 0, "patching_rect": [2033.3474278450012, 88.05969834327698, 24.0, 24.0], "svg": ""}}, {"box": {"id": "obj-39", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 9, "outlettype": ["int", "int", "float", "float", "float", "", "int", "float", ""], "patching_rect": [2033.3474278450012, 135.82089066505432, 156.0, 23.0], "text": "transport @clocksource live"}}, {"box": {"buffername": "Synthgrain-<PERSON><PERSON>er", "chanoffset": 0, "id": "obj-3", "maxclass": "waveform~", "numinlets": 5, "numoutlets": 6, "outlettype": ["float", "float", "float", "float", "list", ""], "patching_rect": [870.0000207424164, 725.7143030166626, 256.0, 64.0], "waveformcolor": [1.0, 1.0, 1.0, 1.0]}}, {"box": {"id": "obj-4", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "patching_rect": [1978.5712115764618, 1276.190336227417, 58.0, 23.0], "text": "loadbang"}}, {"box": {"id": "obj-6", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2109.5235781669617, 1426.190319776535, 50.0, 37.0], "text": "midinote 60 60"}}, {"box": {"id": "obj-165", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1978.5712115764618, 1323.8093786239624, 99.0, 23.0], "text": "steal 1, parallel 1"}}, {"box": {"id": "obj-166", "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2164.285476922989, 1230.9522459506989, 29.5, 23.0], "text": "60"}}, {"box": {"id": "obj-185", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [1978.5712115764618, 1390.476037979126, 50.0, 37.0], "text": "midinote 60 60"}}, {"box": {"id": "obj-186", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2164.285476922989, 1314.2855701446533, 34.0, 23.0], "text": "pack"}}, {"box": {"id": "obj-187", "maxclass": "button", "numinlets": 1, "numoutlets": 1, "outlettype": ["bang"], "parameter_enable": 0, "patching_rect": [2164.285476922989, 1188.095107793808, 24.0, 24.0]}}, {"box": {"id": "obj-188", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 3, "numoutlets": 2, "outlettype": ["float", "float"], "patching_rect": [2164.285476922989, 1276.190336227417, 101.0, 23.0], "text": "makenote 60 100"}}, {"box": {"id": "obj-189", "linecount": 2, "maxclass": "message", "numinlets": 2, "numoutlets": 1, "outlettype": [""], "patching_rect": [2164.285476922989, 1361.9046125411987, 87.0, 37.0], "text": "midinote $1 $1"}}, {"box": {"id": "obj-194", "maxclass": "<PERSON><PERSON><PERSON>", "numinlets": 1, "numoutlets": 2, "outlettype": ["signal", "signal"], "patching_rect": [2164.285476922989, 1426.190319776535, 150.0, 23.0], "text": "poly~ grainGen 16 args #0"}}, {"box": {"attr": "size", "id": "obj-26", "maxclass": "attrui", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [768.0, 639.4761137366295, 150.0, 23.0]}}, {"box": {"attr": "size", "id": "obj-33", "maxclass": "attrui", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [2672.709276199341, 948.0, 150.0, 23.0]}}, {"box": {"attr": "size", "id": "obj-44", "maxclass": "attrui", "numinlets": 1, "numoutlets": 1, "outlettype": [""], "parameter_enable": 0, "patching_rect": [2571.428289413452, 1319.0474743843079, 150.0, 23.0]}}], "lines": [{"patchline": {"destination": ["obj-23", 0], "source": ["obj-216", 0]}}, {"patchline": {"destination": ["obj-1", 0], "source": ["obj-23", 0]}}, {"patchline": {"destination": ["obj-23", 1], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-26", 0], "source": ["obj-28", 0]}}, {"patchline": {"destination": ["obj-28", 0], "source": ["obj-30", 0]}}, {"patchline": {"destination": ["obj-131", 0], "source": ["obj-100", 0]}}, {"patchline": {"destination": ["obj-100", 0], "source": ["obj-101", 0]}}, {"patchline": {"destination": ["obj-132", 0], "source": ["obj-104", 0]}}, {"patchline": {"destination": ["obj-104", 0], "source": ["obj-105", 0]}}, {"patchline": {"destination": ["obj-133", 0], "source": ["obj-108", 0]}}, {"patchline": {"destination": ["obj-25", 1], "source": ["obj-109", 0]}}, {"patchline": {"destination": ["obj-687", 1], "source": ["obj-11", 1]}}, {"patchline": {"destination": ["obj-687", 0], "source": ["obj-11", 0]}}, {"patchline": {"destination": ["obj-108", 0], "source": ["obj-110", 0]}}, {"patchline": {"destination": ["obj-134", 0], "source": ["obj-113", 0]}}, {"patchline": {"destination": ["obj-113", 0], "source": ["obj-114", 0]}}, {"patchline": {"destination": ["obj-73", 0], "source": ["obj-115", 1]}}, {"patchline": {"destination": ["obj-73", 1], "source": ["obj-116", 1]}}, {"patchline": {"destination": ["obj-109", 0], "source": ["obj-117", 0]}}, {"patchline": {"destination": ["obj-117", 0], "source": ["obj-128", 0]}}, {"patchline": {"destination": ["obj-25", 0], "source": ["obj-129", 0]}}, {"patchline": {"destination": ["obj-117", 1], "source": ["obj-130", 0]}}, {"patchline": {"destination": ["obj-117", 2], "source": ["obj-131", 0]}}, {"patchline": {"destination": ["obj-117", 3], "source": ["obj-132", 0]}}, {"patchline": {"destination": ["obj-117", 4], "source": ["obj-133", 0]}}, {"patchline": {"destination": ["obj-117", 5], "source": ["obj-134", 0]}}, {"patchline": {"destination": ["obj-137", 0], "source": ["obj-135", 0]}}, {"patchline": {"destination": ["obj-140", 0], "source": ["obj-136", 0]}}, {"patchline": {"destination": ["obj-57", 7], "source": ["obj-137", 0]}}, {"patchline": {"destination": ["obj-74", 1], "source": ["obj-138", 1]}}, {"patchline": {"destination": ["obj-74", 0], "source": ["obj-138", 0]}}, {"patchline": {"destination": ["obj-57", 6], "source": ["obj-140", 0]}}, {"patchline": {"destination": ["obj-117", 7], "source": ["obj-141", 0]}}, {"patchline": {"destination": ["obj-117", 6], "source": ["obj-142", 0]}}, {"patchline": {"destination": ["obj-141", 0], "source": ["obj-143", 0]}}, {"patchline": {"destination": ["obj-143", 0], "source": ["obj-144", 0]}}, {"patchline": {"destination": ["obj-142", 0], "source": ["obj-145", 0]}}, {"patchline": {"destination": ["obj-145", 0], "source": ["obj-146", 0]}}, {"patchline": {"destination": ["obj-135", 0], "order": 0, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-136", 0], "order": 1, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-80", 0], "order": 8, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-82", 0], "order": 6, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-83", 0], "order": 5, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-84", 0], "order": 3, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-85", 0], "order": 4, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-86", 0], "order": 7, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-87", 0], "order": 2, "source": ["obj-149", 0]}}, {"patchline": {"destination": ["obj-129", 1], "source": ["obj-150", 0]}}, {"patchline": {"destination": ["obj-129", 2], "order": 0, "source": ["obj-151", 0]}}, {"patchline": {"destination": ["obj-221", 1], "order": 1, "source": ["obj-151", 0]}}, {"patchline": {"destination": ["obj-40", 0], "source": ["obj-152", 0]}}, {"patchline": {"destination": ["obj-40", 1], "source": ["obj-153", 0]}}, {"patchline": {"destination": ["obj-149", 0], "source": ["obj-155", 0]}}, {"patchline": {"destination": ["obj-19", 0], "source": ["obj-16", 0]}}, {"patchline": {"destination": ["obj-194", 0], "source": ["obj-165", 0]}}, {"patchline": {"destination": ["obj-188", 0], "source": ["obj-166", 0]}}, {"patchline": {"destination": ["obj-218", 0], "source": ["obj-169", 0]}}, {"patchline": {"destination": ["obj-8", 0], "source": ["obj-17", 0]}}, {"patchline": {"destination": ["obj-171", 0], "order": 1, "source": ["obj-170", 0]}}, {"patchline": {"destination": ["obj-182", 0], "order": 0, "source": ["obj-170", 0]}}, {"patchline": {"destination": ["obj-181", 0], "source": ["obj-171", 0]}}, {"patchline": {"destination": ["obj-170", 0], "source": ["obj-172", 0]}}, {"patchline": {"destination": ["obj-187", 0], "source": ["obj-18", 0]}}, {"patchline": {"destination": ["obj-193", 0], "source": ["obj-181", 0]}}, {"patchline": {"destination": ["obj-183", 0], "source": ["obj-182", 0]}}, {"patchline": {"destination": ["obj-18", 0], "order": 1, "source": ["obj-183", 0]}}, {"patchline": {"destination": ["obj-227", 0], "order": 0, "source": ["obj-183", 0]}}, {"patchline": {"destination": ["obj-189", 0], "source": ["obj-186", 0]}}, {"patchline": {"destination": ["obj-166", 0], "source": ["obj-187", 0]}}, {"patchline": {"destination": ["obj-186", 0], "source": ["obj-188", 0]}}, {"patchline": {"destination": ["obj-185", 1], "order": 2, "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-194", 0], "order": 0, "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-6", 1], "order": 1, "source": ["obj-189", 0]}}, {"patchline": {"destination": ["obj-519", 0], "source": ["obj-19", 0]}}, {"patchline": {"destination": ["obj-169", 0], "order": 3, "source": ["obj-193", 0]}}, {"patchline": {"destination": ["obj-2", 0], "midpoints": [509.50001192092896, 699.0950700044632, 721.0000059604645, 699.0950700044632, 721.0000059604645, 588.0, 743.5, 588.0], "order": 2, "source": ["obj-193", 0]}}, {"patchline": {"destination": ["obj-68", 0], "midpoints": [509.50001192092896, 702.8025349883828, 1152.3571701049805, 702.8025349883828], "order": 0, "source": ["obj-193", 0]}}, {"patchline": {"destination": ["obj-71", 0], "midpoints": [509.50001192092896, 702.6042189085856, 846.6428771018982, 702.6042189085856], "order": 1, "source": ["obj-193", 0]}}, {"patchline": {"destination": ["obj-533", 0], "source": ["obj-194", 1]}}, {"patchline": {"destination": ["obj-533", 1], "source": ["obj-194", 0]}}, {"patchline": {"destination": ["obj-68", 0], "order": 0, "source": ["obj-198", 0]}}, {"patchline": {"destination": ["obj-71", 0], "order": 1, "source": ["obj-198", 0]}}, {"patchline": {"destination": ["obj-151", 0], "order": 1, "source": ["obj-2", 6]}}, {"patchline": {"destination": ["obj-26", 0], "order": 0, "source": ["obj-2", 6]}}, {"patchline": {"destination": ["obj-129", 0], "source": ["obj-208", 0]}}, {"patchline": {"destination": ["obj-129", 0], "source": ["obj-211", 0]}}, {"patchline": {"destination": ["obj-41", 0], "source": ["obj-215", 0]}}, {"patchline": {"destination": ["obj-208", 0], "source": ["obj-218", 0]}}, {"patchline": {"destination": ["obj-222", 0], "source": ["obj-218", 1]}}, {"patchline": {"destination": ["obj-129", 0], "source": ["obj-221", 0]}}, {"patchline": {"destination": ["obj-223", 0], "source": ["obj-222", 0]}}, {"patchline": {"destination": ["obj-211", 0], "source": ["obj-223", 1]}}, {"patchline": {"destination": ["obj-221", 0], "source": ["obj-223", 0]}}, {"patchline": {"destination": ["obj-139", 0], "source": ["obj-226", 0]}}, {"patchline": {"destination": ["obj-228", 0], "source": ["obj-227", 0]}}, {"patchline": {"destination": ["obj-230", 0], "source": ["obj-228", 2]}}, {"patchline": {"destination": ["obj-230", 0], "source": ["obj-228", 0]}}, {"patchline": {"destination": ["obj-18", 0], "order": 1, "source": ["obj-230", 0]}}, {"patchline": {"destination": ["obj-231", 0], "order": 0, "source": ["obj-230", 0]}}, {"patchline": {"destination": ["obj-232", 0], "order": 0, "source": ["obj-231", 0]}}, {"patchline": {"destination": ["obj-234", 0], "order": 1, "source": ["obj-231", 0]}}, {"patchline": {"destination": ["obj-233", 0], "order": 0, "source": ["obj-232", 0]}}, {"patchline": {"destination": ["obj-235", 0], "order": 1, "source": ["obj-232", 0]}}, {"patchline": {"destination": ["obj-236", 0], "source": ["obj-233", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-234", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-235", 0]}}, {"patchline": {"destination": ["obj-18", 0], "source": ["obj-236", 0]}}, {"patchline": {"destination": ["obj-65", 0], "source": ["obj-25", 0]}}, {"patchline": {"destination": ["obj-40", 0], "midpoints": [777.5, 864.8043417930603, 673.7857301235199, 864.8043417930603], "source": ["obj-26", 0]}}, {"patchline": {"destination": ["obj-687", 0], "source": ["obj-32", 0]}}, {"patchline": {"destination": ["obj-483", 0], "source": ["obj-33", 0]}}, {"patchline": {"destination": ["obj-44", 0], "order": 1, "source": ["obj-36", 6]}}, {"patchline": {"destination": ["obj-490", 0], "midpoints": [2690.2391646703086, 1310.2141392230988, 2799.5, 1310.2141392230988], "order": 0, "source": ["obj-36", 6]}}, {"patchline": {"destination": ["obj-687", 0], "source": ["obj-38", 0]}}, {"patchline": {"destination": ["obj-165", 0], "source": ["obj-4", 0]}}, {"patchline": {"destination": ["obj-150", 0], "source": ["obj-40", 0]}}, {"patchline": {"destination": ["obj-151", 0], "source": ["obj-40", 1]}}, {"patchline": {"destination": ["obj-39", 0], "source": ["obj-41", 0]}}, {"patchline": {"destination": ["obj-450", 0], "source": ["obj-43", 0]}}, {"patchline": {"destination": ["obj-481", 0], "source": ["obj-44", 0]}}, {"patchline": {"destination": ["obj-467", 0], "source": ["obj-442", 0]}}, {"patchline": {"destination": ["obj-466", 0], "source": ["obj-450", 0]}}, {"patchline": {"destination": ["obj-519", 0], "midpoints": [2596.9017120599747, 492.71328711509705, 2597.11874628067, 492.71328711509705], "source": ["obj-469", 0]}}, {"patchline": {"destination": ["obj-33", 0], "order": 1, "source": ["obj-476", 0]}}, {"patchline": {"destination": ["obj-491", 0], "midpoints": [2830.9282619953156, 1006.9760845899582, 2805.5, 1006.9760845899582], "order": 0, "source": ["obj-476", 0]}}, {"patchline": {"destination": ["obj-476", 0], "midpoints": [2850.2612888415656, 835.5745358467102, 2830.9282619953156, 835.5745358467102], "source": ["obj-478", 6]}}, {"patchline": {"destination": ["obj-36", 0], "midpoints": [2757.0177878141403, 737.1903663873672, 2596.9956636428833, 737.1903663873672], "order": 1, "source": ["obj-479", 0]}}, {"patchline": {"destination": ["obj-478", 0], "order": 0, "source": ["obj-479", 0]}}, {"patchline": {"destination": ["obj-490", 0], "source": ["obj-481", 1]}}, {"patchline": {"destination": ["obj-513", 0], "source": ["obj-481", 0]}}, {"patchline": {"destination": ["obj-491", 0], "source": ["obj-483", 1]}}, {"patchline": {"destination": ["obj-515", 0], "source": ["obj-483", 0]}}, {"patchline": {"destination": ["obj-494", 0], "source": ["obj-490", 0]}}, {"patchline": {"destination": ["obj-495", 0], "source": ["obj-491", 0]}}, {"patchline": {"destination": ["obj-3", 0], "source": ["obj-5", 0]}}, {"patchline": {"destination": ["obj-57", 1], "source": ["obj-50", 0]}}, {"patchline": {"destination": ["obj-496", 0], "source": ["obj-507", 0]}}, {"patchline": {"destination": ["obj-57", 2], "source": ["obj-51", 0]}}, {"patchline": {"destination": ["obj-497", 0], "source": ["obj-513", 0]}}, {"patchline": {"destination": ["obj-504", 0], "source": ["obj-515", 0]}}, {"patchline": {"destination": ["obj-516", 0], "source": ["obj-517", 0]}}, {"patchline": {"destination": ["obj-519", 0], "midpoints": [2645.61874628067, 493.41328711509686, 2597.11874628067, 493.41328711509686], "source": ["obj-518", 0]}}, {"patchline": {"destination": ["obj-479", 0], "source": ["obj-519", 1]}}, {"patchline": {"destination": ["obj-521", 0], "source": ["obj-522", 0]}}, {"patchline": {"destination": ["obj-522", 1], "source": ["obj-527", 0]}}, {"patchline": {"destination": ["obj-522", 0], "order": 1, "source": ["obj-529", 2]}}, {"patchline": {"destination": ["obj-527", 0], "midpoints": [2994.64253282547, 745.4880059719089, 3028.547287940979, 745.4880059719089], "order": 0, "source": ["obj-529", 2]}}, {"patchline": {"destination": ["obj-57", 3], "source": ["obj-53", 0]}}, {"patchline": {"destination": ["obj-517", 0], "source": ["obj-531", 0]}}, {"patchline": {"destination": ["obj-11", 1], "order": 1, "source": ["obj-533", 1]}}, {"patchline": {"destination": ["obj-11", 0], "order": 1, "source": ["obj-533", 0]}}, {"patchline": {"destination": ["obj-687", 1], "order": 0, "source": ["obj-533", 1]}}, {"patchline": {"destination": ["obj-687", 0], "order": 0, "source": ["obj-533", 0]}}, {"patchline": {"destination": ["obj-57", 4], "source": ["obj-54", 0]}}, {"patchline": {"destination": ["obj-57", 5], "source": ["obj-55", 0]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-56", 0]}}, {"patchline": {"destination": ["obj-101", 0], "source": ["obj-57", 2]}}, {"patchline": {"destination": ["obj-105", 0], "source": ["obj-57", 3]}}, {"patchline": {"destination": ["obj-110", 0], "source": ["obj-57", 4]}}, {"patchline": {"destination": ["obj-114", 0], "source": ["obj-57", 5]}}, {"patchline": {"destination": ["obj-144", 0], "source": ["obj-57", 7]}}, {"patchline": {"destination": ["obj-146", 0], "source": ["obj-57", 6]}}, {"patchline": {"destination": ["obj-93", 0], "source": ["obj-57", 0]}}, {"patchline": {"destination": ["obj-97", 0], "source": ["obj-57", 1]}}, {"patchline": {"destination": ["obj-69", 0], "source": ["obj-65", 0]}}, {"patchline": {"destination": ["obj-65", 1], "source": ["obj-66", 0]}}, {"patchline": {"destination": ["obj-66", 0], "source": ["obj-68", 0]}}, {"patchline": {"destination": ["obj-72", 0], "source": ["obj-69", 0]}}, {"patchline": {"destination": ["obj-172", 0], "source": ["obj-7", 0]}}, {"patchline": {"destination": ["obj-69", 1], "source": ["obj-70", 0]}}, {"patchline": {"destination": ["obj-70", 0], "source": ["obj-71", 0]}}, {"patchline": {"destination": ["obj-115", 0], "source": ["obj-72", 0]}}, {"patchline": {"destination": ["obj-116", 0], "source": ["obj-72", 1]}}, {"patchline": {"destination": ["obj-138", 1], "order": 1, "source": ["obj-73", 1]}}, {"patchline": {"destination": ["obj-138", 0], "order": 1, "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-74", 1], "order": 0, "source": ["obj-73", 1]}}, {"patchline": {"destination": ["obj-74", 0], "order": 0, "source": ["obj-73", 0]}}, {"patchline": {"destination": ["obj-57", 0], "source": ["obj-80", 0]}}, {"patchline": {"destination": ["obj-50", 0], "source": ["obj-82", 0]}}, {"patchline": {"destination": ["obj-51", 0], "source": ["obj-83", 0]}}, {"patchline": {"destination": ["obj-54", 0], "source": ["obj-84", 0]}}, {"patchline": {"destination": ["obj-53", 0], "source": ["obj-85", 0]}}, {"patchline": {"destination": ["obj-56", 0], "source": ["obj-86", 0]}}, {"patchline": {"destination": ["obj-55", 0], "source": ["obj-87", 0]}}, {"patchline": {"destination": ["obj-95", 0], "source": ["obj-93", 0]}}, {"patchline": {"destination": ["obj-128", 0], "source": ["obj-95", 0]}}, {"patchline": {"destination": ["obj-130", 0], "source": ["obj-96", 0]}}, {"patchline": {"destination": ["obj-96", 0], "source": ["obj-97", 0]}}], "originid": "pat-6", "parameters": {"obj-11::obj-23": ["bypass", "bypass", 0], "obj-11::obj-28": ["Size", "Size", 0], "obj-11::obj-3": ["Regen", "Regen", 0], "obj-11::obj-60": ["<PERSON><PERSON>", "<PERSON><PERSON>", 0], "obj-11::obj-62": ["Dry", "Dry", 0], "obj-11::obj-63": ["Early", "Early", 0], "obj-11::obj-64": ["Tail", "Tail", 0], "obj-11::obj-65": ["Spread", "Spread", 0], "obj-11::obj-66": ["Time", "Time", 0], "obj-138::obj-23": ["bypass[1]", "bypass", 0], "obj-138::obj-28": ["Size[1]", "Size", 0], "obj-138::obj-3": ["Regen[1]", "Regen", 0], "obj-138::obj-60": ["Damp[1]", "<PERSON><PERSON>", 0], "obj-138::obj-62": ["Dry[1]", "Dry", 0], "obj-138::obj-63": ["Early[1]", "Early", 0], "obj-138::obj-64": ["Tail[1]", "Tail", 0], "obj-138::obj-65": ["Spread[1]", "Spread", 0], "obj-138::obj-66": ["Time[1]", "Time", 0], "obj-16": ["live.drop", "live.drop", 0], "obj-171": ["live.grid", "live.grid", 0], "obj-182": ["live.grid[1]", "live.grid", 0], "obj-533": ["live.gain~[3]", "live.gain~[16]", 0], "obj-73": ["live.gain~[2]", "live.gain~[2]", 0], "parameterbanks": {"0": {"index": 0, "name": "", "parameters": ["-", "-", "-", "-", "-", "-", "-", "-"]}}, "parameter_overrides": {"obj-138::obj-23": {"parameter_longname": "bypass[1]"}, "obj-138::obj-28": {"parameter_longname": "Size[1]"}, "obj-138::obj-3": {"parameter_longname": "Regen[1]"}, "obj-138::obj-60": {"parameter_longname": "Damp[1]"}, "obj-138::obj-62": {"parameter_longname": "Dry[1]"}, "obj-138::obj-63": {"parameter_longname": "Early[1]"}, "obj-138::obj-64": {"parameter_longname": "Tail[1]"}, "obj-138::obj-65": {"parameter_longname": "Spread[1]"}, "obj-138::obj-66": {"parameter_longname": "Time[1]"}}, "inherited_shortname": 1}, "dependency_cache": [{"name": "bp.Gigaverb.maxpat", "bootpath": "C74:/packages/Beap/clippings/BEAP/Effects", "type": "JSON", "implicit": 1}, {"name": "grainGen.maxpat", "bootpath": "~/Documents/MAX 1M/LooperNew", "patcherrelativepath": ".", "type": "JSON", "implicit": 1}, {"name": "pan2.maxpat", "bootpath": "~/AppData/Roaming/Cycling '74/Max 9/examples/spatialization/panning/lib", "patcherrelativepath": "../../../AppData/Roaming/Cycling '74/Max 9/examples/spatialization/panning/lib", "type": "JSON", "implicit": 1}], "autosave": 0, "styles": [{"name": "rnbodefault", "default": {"accentcolor": [0.343034118413925, 0.506230533123016, 0.86220508813858, 1.0], "bgcolor": [0.031372549019608, 0.125490196078431, 0.211764705882353, 1.0], "bgfillcolor": {"angle": 270.0, "autogradient": 0.0, "color": [0.031372549019608, 0.125490196078431, 0.211764705882353, 1.0], "color1": [0.031372549019608, 0.125490196078431, 0.211764705882353, 1.0], "color2": [0.263682, 0.004541, 0.038797, 1.0], "proportion": 0.39, "type": "color"}, "color": [0.929412, 0.929412, 0.352941, 1.0], "elementcolor": [0.357540726661682, 0.515565991401672, 0.861786782741547, 1.0], "fontname": ["<PERSON><PERSON>"], "fontsize": [12.0], "stripecolor": [0.258338063955307, 0.352425158023834, 0.511919498443604, 1.0], "textcolor_inverse": [0.968627, 0.968627, 0.968627, 1]}, "parentstyle": "", "multi": 0}, {"name": "rnbohighcontrast", "default": {"accentcolor": [0.666666666666667, 0.666666666666667, 0.666666666666667, 1.0], "bgcolor": [0.0, 0.0, 0.0, 1.0], "bgfillcolor": {"angle": 270.0, "autogradient": 0.0, "color": [0.0, 0.0, 0.0, 1.0], "color1": [0.090196078431373, 0.090196078431373, 0.090196078431373, 1.0], "color2": [0.156862745098039, 0.168627450980392, 0.164705882352941, 1.0], "proportion": 0.5, "type": "color"}, "clearcolor": [1.0, 1.0, 1.0, 0.0], "color": [1.0, 0.874509803921569, 0.141176470588235, 1.0], "editing_bgcolor": [0.258823529411765, 0.258823529411765, 0.258823529411765, 1.0], "elementcolor": [0.223386004567146, 0.254748553037643, 0.998085916042328, 1.0], "fontsize": [13.0], "locked_bgcolor": [0.258823529411765, 0.258823529411765, 0.258823529411765, 1.0], "selectioncolor": [0.301960784313725, 0.694117647058824, 0.949019607843137, 1.0], "stripecolor": [0.258823529411765, 0.258823529411765, 0.258823529411765, 1.0], "textcolor": [1.0, 1.0, 1.0, 1.0], "textcolor_inverse": [1.0, 1.0, 1.0, 1.0]}, "parentstyle": "", "multi": 0}, {"name": "rnbolight", "default": {"accentcolor": [0.443137254901961, 0.505882352941176, 0.556862745098039, 1.0], "bgcolor": [0.796078431372549, 0.862745098039216, 0.925490196078431, 1.0], "bgfillcolor": {"angle": 270.0, "autogradient": 0.0, "color": [0.835294117647059, 0.901960784313726, 0.964705882352941, 1.0], "color1": [0.031372549019608, 0.125490196078431, 0.211764705882353, 1.0], "color2": [0.263682, 0.004541, 0.038797, 1.0], "proportion": 0.39, "type": "color"}, "clearcolor": [0.898039, 0.898039, 0.898039, 1.0], "color": [0.815686274509804, 0.509803921568627, 0.262745098039216, 1.0], "editing_bgcolor": [0.898039, 0.898039, 0.898039, 1.0], "elementcolor": [0.337254901960784, 0.384313725490196, 0.462745098039216, 1.0], "fontname": ["<PERSON><PERSON>"], "locked_bgcolor": [0.898039, 0.898039, 0.898039, 1.0], "stripecolor": [0.309803921568627, 0.698039215686274, 0.764705882352941, 1.0], "textcolor_inverse": [0.0, 0.0, 0.0, 1.0]}, "parentstyle": "", "multi": 0}, {"name": "rnbomonokai", "default": {"accentcolor": [0.501960784313725, 0.501960784313725, 0.501960784313725, 1.0], "bgcolor": [0.0, 0.0, 0.0, 1.0], "bgfillcolor": {"angle": 270.0, "autogradient": 0.0, "color": [0.0, 0.0, 0.0, 1.0], "color1": [0.031372549019608, 0.125490196078431, 0.211764705882353, 1.0], "color2": [0.263682, 0.004541, 0.038797, 1.0], "proportion": 0.39, "type": "color"}, "clearcolor": [0.976470588235294, 0.96078431372549, 0.917647058823529, 1.0], "color": [0.611764705882353, 0.125490196078431, 0.776470588235294, 1.0], "editing_bgcolor": [0.976470588235294, 0.96078431372549, 0.917647058823529, 1.0], "elementcolor": [0.749019607843137, 0.83921568627451, 1.0, 1.0], "fontname": ["<PERSON><PERSON>"], "locked_bgcolor": [0.976470588235294, 0.96078431372549, 0.917647058823529, 1.0], "stripecolor": [0.796078431372549, 0.207843137254902, 1.0, 1.0], "textcolor": [0.129412, 0.129412, 0.129412, 1.0]}, "parentstyle": "", "multi": 0}]}}